/**
 * KOS Trading Bot - Content Script
 *
 * This script runs in the context of the Pocket Option website and handles:
 * 1. Reading market data from the page
 * 2. Executing trades based on bot signals
 * 3. Detecting trade outcomes
 * 4. Communicating with the extension popup
 * 5. Managing the floating interface
 */

// Global variables
let isConnected = false;
let observingTrades = false;
let currentBalance = 0;
let activeTrades = [];
let floatingWindow = null;
let floatingFrame = null;
let lastTradeWasWin = true; // Track if the last trade was a win
let martingaleSequenceIndex = 0; // Track current position in martingale sequence

// Martingale sequence for trade amounts after losses
const martingaleSequence = [
    1.00,   // Initial amount
    2.17,   // After 1 loss
    4.54,   // After 2 losses
    9.47,   // After 3 losses
    19.76,  // After 4 losses
    41.224, // After 5 losses
    86.06,  // After 6 losses
    179.61, // After 7 losses
    179.61, // After 8 losses (same as previous)
    374.89, // After 9 losses
    782.12, // After 10 losses
    1630.55, // After 11 losses
    3400.49, // After 12 losses
    7095.23  // After 13 losses
];

// Function to get the current trade amount based on martingale sequence
function getMartingaleAmount() {
    return martingaleSequence[martingaleSequenceIndex];
}

// Function to update martingale sequence index based on trade result
function updateMartingaleIndex(isWin) {
    if (isWin) {
        // Reset to initial amount after a win
        martingaleSequenceIndex = 0;
        lastTradeWasWin = true;
    } else {
        // Move to next amount in sequence after a loss, but don't exceed array length
        martingaleSequenceIndex = Math.min(martingaleSequenceIndex + 1, martingaleSequence.length - 1);
        lastTradeWasWin = false;
    }
    console.log(`Martingale updated: isWin=${isWin}, new index=${martingaleSequenceIndex}, next amount=${getMartingaleAmount()}`);
}

let settings = {};
let pocketOptionSelectors = {
    // Balance selectors - updated with more specific selectors for Pocket Option
    balanceSelector: [
        '.balance-value',
        '.balance-amount',
        '.account-balance',
        '.balance',
        '[data-role="balance"]',
        '.header-balance'
    ],

    // Asset selectors
    assetSelectorBtn: [
        '.asset-selector',
        '.asset-select',
        '.asset-dropdown-toggle',
        '[data-role="asset-selector"]'
    ],
    assetDropdown: '.asset-dropdown',
    assetItems: '.asset-item',

    // Amount selectors - updated with more specific selectors for Pocket Option
    amountInput: [
        // Specific selectors based on the provided HTML structure
        '.block--bet-amount .value__val input',
        '.block--bet-amount input',
        '.control__value input',
        '.value__val input',
        // Original selectors as fallback
        '.amount-input',
        'input[name="amount"]',
        'input.amount',
        'input[placeholder*="amount"]',
        'input[type="number"]'
    ],

    // Expiry selectors
    expirySelector: [
        '.expiry-selector',
        '.expiry-dropdown',
        '.expiration-selector',
        '[data-role="expiry-selector"]'
    ],
    expiryDropdown: '.expiry-dropdown',
    expiryItems: '.expiry-item',

    // Trade buttons
    buyButton: [
        '.buy-button',
        '.call-button',
        '.up-button',
        '.higher-button',
        'button.green',
        'button.buy',
        'button.call',
        'button[data-action="buy"]',
        'button[data-action="call"]'
    ],
    sellButton: [
        '.sell-button',
        '.put-button',
        '.down-button',
        '.lower-button',
        'button.red',
        'button.sell',
        'button.put',
        'button[data-action="sell"]',
        'button[data-action="put"]'
    ],

    // Trade history
    tradeHistoryContainer: [
        '.trade-history',
        '.history-container',
        '.trades-history',
        '.closed-trades'
    ],
    tradeItems: [
        '.trade-item',
        '.history-item',
        '.trade-history-item'
    ],
    tradeResult: [
        '.trade-result',
        '.result',
        '.outcome',
        '.win',
        '.loss'
    ],

    // Active trades
    activeTradesContainer: [
        '.active-trades',
        '.open-trades',
        '.current-trades'
    ],
    activeTradeItems: [
        '.active-trade-item',
        '.open-trade-item',
        '.current-trade'
    ]
};

// Set a global flag to indicate that our content script is running
window.botContentScriptInjected = true;

// Initialize when the page is fully loaded
window.addEventListener('load', function() {
    console.log('KOS Trading Bot content script loaded');

    // Check if we're on Pocket Option
    if (window.location.href.includes('pocketoption.com')) {
        // Wait a bit for the UI to fully initialize
        setTimeout(() => {
            // Initialize the bot
            initializeBot();

            // Check if we should automatically open the floating interface
            chrome.storage.local.get('navigatingFromExtension', function(data) {
                if (data.navigatingFromExtension) {
                    // Reset the flag
                    chrome.storage.local.set({navigatingFromExtension: false});

                    // Wait a bit more for everything to be ready
                    setTimeout(() => {
                        // Create floating interface
                        createFloatingInterface();

                        // Load settings
                        loadSettings();

                        // Show success notification
                        showNotification('KOS Trading Bot activated', 'success');
                    }, 1000);
                }
            });
        }, 2000);
    }
});

// Listen for the custom event from the background script
document.addEventListener('botContentScriptInitialize', function() {
    console.log('Received initialization event from background script');
    initializeBot();
});

// Listen for the custom event to create floating interface
document.addEventListener('createFloatingInterface', function() {
    console.log('Received createFloatingInterface event');
    forceCreateFloatingInterface();
});

// Initialize the bot
function initializeBot() {
    console.log('Initializing KOS Trading Bot');

    if (isConnected) {
        console.log('Bot already connected');
        return true;
    }

    // IMPROVED: Create the floating interface immediately to show the user something is happening
    // This way the user sees the interface even while we're still detecting the balance
    console.log('Creating floating interface immediately');
    createFloatingInterface();

    // Inject a script into the page to access the page's JavaScript context
    injectPageScript();

    // IMPROVED: Use a more aggressive approach to find the balance
    // Create a script to directly extract balance from the page context
    const balanceScript = document.createElement('script');
    balanceScript.textContent = `
        (function() {
            try {
                // Try multiple approaches to find the balance

                // 1. Look for specific Pocket Option balance elements
                const balanceElements = document.querySelectorAll('.js-hd.js-balance-demo, [data-hd-show], .js-hd, .balance-value, .balance-amount, .account-balance, .balance, [data-role="balance"], .header-balance, .js-balance');

                for (const element of balanceElements) {
                    // Check for data-hd-show attribute
                    if (element.hasAttribute('data-hd-show')) {
                        const balanceValue = element.getAttribute('data-hd-show');
                        if (balanceValue && balanceValue !== '*******') {
                            const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                            if (!isNaN(parsedBalance)) {
                                console.log('Found balance from data-hd-show:', parsedBalance);
                                window.dispatchEvent(new CustomEvent('balanceDetected', {
                                    detail: { balance: parsedBalance, source: 'data-hd-show' }
                                }));
                                return;
                            }
                        }
                    }

                    // Check text content
                    const text = element.textContent.trim();
                    if (text && text !== '*******' && /[$€£]?\\s*[\\d,.]+/.test(text)) {
                        // Extract the numeric part
                        const numericMatch = text.match(/[\\d,.]+/);
                        if (numericMatch) {
                            const parsedBalance = parseFloat(numericMatch[0].replace(/,/g, ''));
                            if (!isNaN(parsedBalance)) {
                                console.log('Found balance from text content:', parsedBalance);
                                window.dispatchEvent(new CustomEvent('balanceDetected', {
                                    detail: { balance: parsedBalance, source: 'text-content' }
                                }));
                                return;
                            }
                        }
                    }
                }

                // 2. Look for any elements containing currency symbols
                const currencyElements = document.querySelectorAll('*:contains("$"), *:contains("€"), *:contains("£")');
                for (const element of currencyElements) {
                    const text = element.textContent.trim();
                    if (text && /[$€£]?\\s*[\\d,.]+/.test(text)) {
                        // Extract the numeric part
                        const numericMatch = text.match(/[\\d,.]+/);
                        if (numericMatch) {
                            const parsedBalance = parseFloat(numericMatch[0].replace(/,/g, ''));
                            if (!isNaN(parsedBalance) && parsedBalance > 0) {
                                console.log('Found potential balance from currency element:', parsedBalance);
                                window.dispatchEvent(new CustomEvent('balanceDetected', {
                                    detail: { balance: parsedBalance, source: 'currency-element' }
                                }));
                                return;
                            }
                        }
                    }
                }

                // 3. Try to access global variables that might contain the balance
                if (window.balance !== undefined) {
                    console.log('Found balance from global variable:', window.balance);
                    window.dispatchEvent(new CustomEvent('balanceDetected', {
                        detail: { balance: window.balance, source: 'global-variable' }
                    }));
                    return;
                }

                console.log('Could not find balance using script approach');
            } catch(e) {
                console.error('Error in balance detection script:', e);
            }
        })();
    `;
    document.head.appendChild(balanceScript);
    balanceScript.remove();

    // Listen for the balance detected event
    window.addEventListener('balanceDetected', function(event) {
        const { balance, source } = event.detail;
        console.log(`Balance detected via ${source}: ${balance}`);
        currentBalance = balance;

        // Update the floating interface with the detected balance
        updateFloatingInterfaceBalance(currentBalance, true);

        // Show a notification
        showNotification(`Balance detected: $${balance.toFixed(2)}`, 'success');

        // Set up observers for future balance changes
        setupBalanceObserver();
    });

    // Perform a direct scan of the page to find interactive elements
    scanPageForInteractiveElements();

    // First try to find the specific Pocket Option balance element
    const pocketOptionBalance = findPocketOptionBalance();
    if (pocketOptionBalance) {
        console.log('Found Pocket Option balance element');

        // We found the specific balance element, so we're definitely on the trading page
        isConnected = true;

        // Set up observers
        setupBalanceObserver();
        setupTradeObserver();

        // Set up trading buttons detection
        findTradingButtons();

        // Notify the popup that we're ready
        chrome.runtime.sendMessage({
            action: 'connectionEstablished',
            balance: currentBalance
        });

        // Show a notification
        showNotification('KOS Trading Bot activated', 'success');

        // Demonstrate that the bot can interact with the page
        demonstrateInteraction();

        return true;
    }

    // If we didn't find the specific Pocket Option balance element, try the generic approach
    const balanceElement = findElement(pocketOptionSelectors.balanceSelector);

    if (balanceElement) {
        console.log('Found balance element, we are on the trading page');

        // Read initial balance
        currentBalance = parseBalance(balanceElement.textContent);
        console.log('Current balance:', currentBalance);

        // Set up observers
        setupBalanceObserver();
        setupTradeObserver();

        // Try to get balance directly from page context
        getBalanceFromPage();

        // Set up trading buttons detection
        findTradingButtons();

        // Notify the popup that we're ready
        chrome.runtime.sendMessage({
            action: 'connectionEstablished',
            balance: currentBalance
        });

        isConnected = true;

        // Show a notification
        showNotification('KOS Trading Bot activated', 'success');

        // Demonstrate that the bot can interact with the page
        demonstrateInteraction();

        return true;
    } else {
        console.log('Could not find balance element, not on trading page');

        // Try to inject script and get balance directly
        injectPageScript();
        getBalanceFromPage();

        // Perform a direct scan of the page to find interactive elements
        scanPageForInteractiveElements();

        // Show a notification
        showNotification('Trading Bot activated in limited mode', 'info');

        // Demonstrate that the bot can interact with the page
        demonstrateInteraction();

        return false;
    }
}

// Find the specific Pocket Option balance element
function findPocketOptionBalance() {
    console.log('Looking for specific Pocket Option balance element...');

    // Look for the exact structure provided by the user
    // <span class="js-hd js-balance-demo" data-hd-hide="*******" data-hd-show="49,978.40" data-hd-status="show">49,978.40</span>
    console.log('Searching for Pocket Option balance elements with specific selectors');

    // Log all elements with data-hd-show attribute for debugging
    const allDataHdShowElements = document.querySelectorAll('[data-hd-show]');
    console.log(`Found ${allDataHdShowElements.length} elements with data-hd-show attribute`);
    for (let i = 0; i < allDataHdShowElements.length; i++) {
        const el = allDataHdShowElements[i];
        console.log(`Element ${i+1} with data-hd-show:`, {
            tagName: el.tagName,
            className: el.className,
            id: el.id,
            'data-hd-show': el.getAttribute('data-hd-show'),
            'data-hd-status': el.getAttribute('data-hd-status'),
            textContent: el.textContent
        });
    }

    // Use a broader set of selectors to find the balance element
    const pocketOptionBalanceElements = document.querySelectorAll('.js-hd.js-balance-demo, [data-hd-show], .js-hd, .balance-value, .balance-amount, .account-balance, .balance, [data-role="balance"], .header-balance, .js-balance');

    if (pocketOptionBalanceElements.length > 0) {
        console.log(`Found ${pocketOptionBalanceElements.length} potential Pocket Option balance elements`);

        for (const element of pocketOptionBalanceElements) {
            // Check for the data-hd-show attribute which contains the balance
            if (element.hasAttribute('data-hd-show')) {
                const balanceValue = element.getAttribute('data-hd-show');
                if (balanceValue && balanceValue !== '*******') {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                    if (!isNaN(parsedBalance)) {
                        console.log('Found Pocket Option balance from data-hd-show:', parsedBalance);
                        console.log('Balance element details:', {
                            tagName: element.tagName,
                            className: element.className,
                            id: element.id,
                            attributes: Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', '),
                            textContent: element.textContent,
                            innerHTML: element.innerHTML
                        });

                        // Highlight the element temporarily to help debugging
                        element.style.border = '3px solid #00ff00';
                        element.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';

                        // Show notification about balance found
                        showNotification(`Found balance: $${parsedBalance.toFixed(2)}`, 'success');

                        // Update the balance
                        currentBalance = parsedBalance;
                        updateFloatingInterfaceBalance(currentBalance, true);

                        // Set up a specific observer for this element
                        setupPocketOptionBalanceObserver(element);

                        return element;
                    }
                }
            }

            // If no data-hd-show attribute or it's masked, try the text content
            const text = element.textContent.trim();
            if (text && text !== '*******' && /[\d,.]+/.test(text)) {
                // Parse the balance value, handling commas
                const parsedBalance = parseFloat(text.replace(/,/g, ''));
                if (!isNaN(parsedBalance)) {
                    console.log('Found Pocket Option balance from text content:', parsedBalance);

                    // Don't highlight the element

                    // Don't show notification

                    // Update the balance
                    currentBalance = parsedBalance;
                    updateFloatingInterfaceBalance(currentBalance);

                    // Set up a specific observer for this element
                    setupPocketOptionBalanceObserver(element);

                    return element;
                }
            }
        }
    }

    return null;
}

// Set up a specific observer for the Pocket Option balance element
function setupPocketOptionBalanceObserver(element) {
    console.log('Setting up specific observer for Pocket Option balance element');

    // Create a MutationObserver to watch for changes to the element
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            // Check for attribute changes
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-hd-show') {
                const balanceValue = element.getAttribute('data-hd-show');
                if (balanceValue && balanceValue !== '*******') {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                    if (!isNaN(parsedBalance) && parsedBalance !== currentBalance) {
                        console.log('Balance changed (attribute):', currentBalance, '->', parsedBalance);
                        currentBalance = parsedBalance;
                        updateFloatingInterfaceBalance(currentBalance);
                    }
                }
            }

            // Check for text content changes
            if (mutation.type === 'characterData' || mutation.type === 'childList') {
                const text = element.textContent.trim();
                if (text && text !== '*******' && /[\d,.]+/.test(text)) {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(text.replace(/,/g, ''));
                    if (!isNaN(parsedBalance) && parsedBalance !== currentBalance) {
                        console.log('Balance changed (text):', currentBalance, '->', parsedBalance);
                        currentBalance = parsedBalance;
                        updateFloatingInterfaceBalance(currentBalance);
                    }
                }
            }
        });
    });

    // Start observing the element for changes
    observer.observe(element, {
        attributes: true,
        attributeFilter: ['data-hd-show', 'data-hd-status'],
        characterData: true,
        childList: true,
        subtree: true
    });

    console.log('Pocket Option balance observer set up');

    // Also set up a periodic check for changes, but very infrequently
    setInterval(() => {
        try {
            // Check attribute
            if (element.hasAttribute('data-hd-show')) {
                const balanceValue = element.getAttribute('data-hd-show');
                if (balanceValue && balanceValue !== '*******') {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                    if (!isNaN(parsedBalance) && Math.abs(parsedBalance - currentBalance) > 0.01) {
                        console.log('Balance changed (interval check attribute):', currentBalance, '->', parsedBalance);
                        currentBalance = parsedBalance;
                        updateFloatingInterfaceBalance(currentBalance);
                    }
                }
            }

            // Check text content
            const text = element.textContent.trim();
            if (text && text !== '*******' && /[\d,.]+/.test(text)) {
                // Parse the balance value, handling commas
                const parsedBalance = parseFloat(text.replace(/,/g, ''));
                if (!isNaN(parsedBalance) && Math.abs(parsedBalance - currentBalance) > 0.01) {
                    console.log('Balance changed (interval check text):', currentBalance, '->', parsedBalance);
                    currentBalance = parsedBalance;
                    updateFloatingInterfaceBalance(currentBalance);
                }
            }
        } catch (error) {
            console.error('Error in balance check interval:', error);
            // Don't throw errors that could break the interval
        }
    }, 60000); // Check only once per minute
}

// Scan the page for interactive elements
function scanPageForInteractiveElements() {
    console.log('Scanning page for interactive elements...');

    // Create a visual overlay to show what we're scanning
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
    overlay.style.zIndex = '9999998';
    overlay.style.pointerEvents = 'none';
    document.body.appendChild(overlay);

    // Find all buttons on the page
    const allButtons = document.querySelectorAll('button, [role="button"], .btn, .button');
    console.log(`Found ${allButtons.length} buttons on the page`);

    // Find all inputs on the page
    const allInputs = document.querySelectorAll('input');
    console.log(`Found ${allInputs.length} inputs on the page`);

    // Find potential trading buttons
    const potentialBuyButtons = [];
    const potentialSellButtons = [];

    allButtons.forEach(button => {
        const text = button.textContent?.toLowerCase() || '';
        const classes = button.className?.toLowerCase() || '';
        const style = window.getComputedStyle(button);
        const backgroundColor = style.backgroundColor;

        // Highlight the button temporarily
        const originalBackground = button.style.backgroundColor;
        const originalBorder = button.style.border;

        // Check if it might be a buy button
        if (text.includes('buy') || text.includes('call') || text.includes('up') ||
            classes.includes('buy') || classes.includes('call') || classes.includes('up') ||
            backgroundColor.includes('green') || backgroundColor.includes('rgb(0, 128') ||
            backgroundColor.includes('rgb(0, 255')) {

            potentialBuyButtons.push(button);

            // Highlight buy buttons in green
            button.style.border = '2px solid #00ff00';
            setTimeout(() => {
                button.style.backgroundColor = originalBackground;
                button.style.border = originalBorder;
            }, 3000);
        }

        // Check if it might be a sell button
        if (text.includes('sell') || text.includes('put') || text.includes('down') ||
            classes.includes('sell') || classes.includes('put') || classes.includes('down') ||
            backgroundColor.includes('red') || backgroundColor.includes('rgb(255, 0') ||
            backgroundColor.includes('rgb(128, 0')) {

            potentialSellButtons.push(button);

            // Highlight sell buttons in red
            button.style.border = '2px solid #ff0000';
            setTimeout(() => {
                button.style.backgroundColor = originalBackground;
                button.style.border = originalBorder;
            }, 3000);
        }
    });

    console.log(`Found ${potentialBuyButtons.length} potential buy buttons`);
    console.log(`Found ${potentialSellButtons.length} potential sell buttons`);

    // Find potential amount inputs
    const potentialAmountInputs = [];

    allInputs.forEach(input => {
        const type = input.type?.toLowerCase() || '';
        const placeholder = input.placeholder?.toLowerCase() || '';
        const name = input.name?.toLowerCase() || '';
        const id = input.id?.toLowerCase() || '';
        const classes = input.className?.toLowerCase() || '';

        // Check if it might be an amount input
        if (type === 'number' ||
            placeholder.includes('amount') ||
            name.includes('amount') ||
            id.includes('amount') ||
            classes.includes('amount')) {

            potentialAmountInputs.push(input);

            // Highlight amount inputs in blue
            const originalBorder = input.style.border;
            input.style.border = '2px solid #0000ff';
            setTimeout(() => {
                input.style.border = originalBorder;
            }, 3000);
        }
    });

    console.log(`Found ${potentialAmountInputs.length} potential amount inputs`);

    // Store the found elements for later use
    window.pocketOptionBotElements = {
        buyButtons: potentialBuyButtons,
        sellButtons: potentialSellButtons,
        amountInputs: potentialAmountInputs
    };

    // Remove the overlay after a delay
    setTimeout(() => {
        document.body.removeChild(overlay);
    }, 3000);

    // Silently store the results without showing notification for Neural Pulse mode
    // showNotification(`Found ${potentialBuyButtons.length} buy buttons, ${potentialSellButtons.length} sell buttons, and ${potentialAmountInputs.length} amount inputs`, 'info');

    return {
        buyButtons: potentialBuyButtons,
        sellButtons: potentialSellButtons,
        amountInputs: potentialAmountInputs
    };
}

// Demonstrate that the bot can interact with the page
function demonstrateInteraction() {
    console.log('Demonstrating interaction capabilities...');

    // Get the elements we found earlier
    const elements = window.pocketOptionBotElements || scanPageForInteractiveElements();

    // First, try to find and extract the balance
    extractBalanceFromDOM();

    if (elements.amountInputs.length > 0) {
        // Show that we can interact with the amount input
        const amountInput = elements.amountInputs[0];

        // Save the original value
        const originalValue = amountInput.value;

        // Highlight the input
        highlightElement(amountInput);

        // Set a temporary value to demonstrate interaction
        setTimeout(() => {
            try {
                // Focus the input
                amountInput.focus();

                // Set a temporary value
                amountInput.value = '10.00';
                amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                amountInput.dispatchEvent(new Event('change', { bubbles: true }));

                showNotification('Successfully interacted with amount input', 'success');

                // Restore the original value after a delay
                setTimeout(() => {
                    amountInput.value = originalValue;
                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
                }, 3000);
            } catch (e) {
                console.error('Error demonstrating input interaction:', e);
            }
        }, 1000);
    }

    // Don't show or highlight balance elements
}

// Extract balance from DOM by looking at all elements
function extractBalanceFromDOM() {
    console.log('Extracting balance from DOM...');

    // Create a visual overlay to show we're scanning for balance
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
    overlay.style.zIndex = '9999998';
    overlay.style.pointerEvents = 'none';
    document.body.appendChild(overlay);

    // Common balance-related keywords and patterns
    const balanceKeywords = ['balance', 'account', 'funds', 'wallet', 'deposit', 'money', 'cash', 'total'];
    const currencySymbols = ['$', '€', '£', '¥', '₹', '₽', '₴', '₿', 'USD', 'EUR', 'GBP'];

    // FIRST PRIORITY: Look for the exact structure provided by the user
    // <span class="js-hd js-balance-demo" data-hd-hide="*******" data-hd-show="49,978.40" data-hd-status="show">49,978.40</span>
    const pocketOptionBalanceElements = document.querySelectorAll('.js-balance-demo, [data-hd-show], .js-hd');
    if (pocketOptionBalanceElements.length > 0) {
        console.log(`Found ${pocketOptionBalanceElements.length} Pocket Option balance elements`);
        for (const element of pocketOptionBalanceElements) {
            // Check for the data-hd-show attribute which contains the balance
            if (element.hasAttribute('data-hd-show')) {
                const balanceValue = element.getAttribute('data-hd-show');
                if (balanceValue && balanceValue !== '*******') {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                    if (!isNaN(parsedBalance)) {
                        console.log('Found Pocket Option balance from data-hd-show:', parsedBalance);

                        // Don't highlight the element

                        // Don't show notification

                        // Update the balance
                        currentBalance = parsedBalance;
                        updateFloatingInterfaceBalance(currentBalance);

                        // Remove overlay
                        setTimeout(() => {
                            document.body.removeChild(overlay);
                        }, 3000);

                        return;
                    }
                }
            }

            // If no data-hd-show attribute or it's masked, try the text content
            const text = element.textContent.trim();
            if (text && text !== '*******' && /[\d,.]+/.test(text)) {
                // Parse the balance value, handling commas
                const parsedBalance = parseFloat(text.replace(/,/g, ''));
                if (!isNaN(parsedBalance)) {
                    console.log('Found Pocket Option balance from text content:', parsedBalance);

                    // Don't highlight the element

                    // Don't show notification

                    // Update the balance
                    currentBalance = parsedBalance;
                    updateFloatingInterfaceBalance(currentBalance);

                    // Remove overlay
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 3000);

                    return;
                }
            }
        }
    }

    // Look for elements in the header first
    const headerElements = document.querySelectorAll('header, .header, #header, [class*="header"], .top-bar, .navbar, .nav');
    let foundInHeader = false;

    for (const header of headerElements) {
        // Look for elements with currency symbols in the header
        const elementsWithCurrency = Array.from(header.querySelectorAll('*')).filter(el => {
            const text = el.textContent.trim();
            return text && text.length < 30 && currencySymbols.some(symbol => text.includes(symbol));
        });

        for (const element of elementsWithCurrency) {
            const text = element.textContent.trim();
            console.log('Potential balance in header:', text);

            // Don't highlight the element

            // Try to extract the balance value
            const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
            if (matches && matches[1]) {
                const balanceValue = parseFloat(matches[1].replace(',', '.'));
                if (!isNaN(balanceValue)) {
                    console.log('Extracted balance value from header:', balanceValue);

                    // Don't show notification

                    // Update the balance
                    currentBalance = balanceValue;
                    updateFloatingInterfaceBalance(currentBalance);

                    foundInHeader = true;
                    break;
                }
            }
        }

        if (foundInHeader) break;
    }

    // If we didn't find the balance in the header, look for it in the sidebar
    if (!foundInHeader) {
        const sidebarElements = document.querySelectorAll('.sidebar, #sidebar, [class*="sidebar"], .side-panel, .panel');
        let foundInSidebar = false;

        for (const sidebar of sidebarElements) {
            // Look for elements with currency symbols in the sidebar
            const elementsWithCurrency = Array.from(sidebar.querySelectorAll('*')).filter(el => {
                const text = el.textContent.trim();
                return text && text.length < 30 && currencySymbols.some(symbol => text.includes(symbol));
            });

            for (const element of elementsWithCurrency) {
                const text = element.textContent.trim();
                console.log('Potential balance in sidebar:', text);

                // Don't highlight the element

                // Try to extract the balance value
                const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
                if (matches && matches[1]) {
                    const balanceValue = parseFloat(matches[1].replace(',', '.'));
                    if (!isNaN(balanceValue)) {
                        console.log('Extracted balance value from sidebar:', balanceValue);

                        // Don't show notification

                        // Update the balance
                        currentBalance = balanceValue;
                        updateFloatingInterfaceBalance(currentBalance);

                        foundInSidebar = true;
                        break;
                    }
                }
            }

            if (foundInSidebar) break;
        }
    }

    // If we still haven't found the balance, look for it in any element with a currency symbol
    if (!foundInHeader && !foundInSidebar) {
        // Find all elements with currency symbols
        const elementsWithCurrency = Array.from(document.querySelectorAll('*')).filter(el => {
            const text = el.textContent.trim();
            return text && text.length < 30 && currencySymbols.some(symbol => text.includes(symbol));
        });

        // Sort elements by how likely they are to contain the balance
        const scoredElements = elementsWithCurrency.map(element => {
            const text = element.textContent.trim();
            let score = 0;

            // Check if the text contains a number
            if (/\d+(\.\d+)?/.test(text)) score += 5;

            // Check if the text contains a currency symbol
            if (currencySymbols.some(symbol => text.includes(symbol))) score += 10;

            // Check if the element or its parent has balance-related class or ID
            const elementClasses = element.className ? element.className.toLowerCase() : '';
            const elementId = element.id ? element.id.toLowerCase() : '';
            const parentClasses = element.parentElement ? element.parentElement.className.toLowerCase() : '';
            const parentId = element.parentElement ? element.parentElement.id.toLowerCase() : '';

            if (balanceKeywords.some(keyword =>
                elementClasses.includes(keyword) || elementId.includes(keyword) ||
                parentClasses.includes(keyword) || parentId.includes(keyword)
            )) {
                score += 15;
            }

            // Additional checks for balance-related text
            for (const keyword of balanceKeywords) {
                if (text.toLowerCase().includes(keyword)) {
                    score += 5;
                }
            }

            return { element, score, text };
        }).sort((a, b) => b.score - a.score);

        // Log the top 5 elements
        for (let i = 0; i < Math.min(5, scoredElements.length); i++) {
            const { score, text } = scoredElements[i];
            console.log(`Potential balance element ${i+1}:`, text, 'Score:', score);

            // Don't highlight the element

            // Try to extract the balance value
            const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
            if (matches && matches[1]) {
                const balanceValue = parseFloat(matches[1].replace(',', '.'));
                if (!isNaN(balanceValue)) {
                    console.log('Extracted balance value:', balanceValue);

                    // Don't show notification

                    // Update the balance
                    currentBalance = balanceValue;
                    updateFloatingInterfaceBalance(currentBalance);

                    break;
                }
            }
        }
    }

    // Remove the overlay after a delay
    setTimeout(() => {
        document.body.removeChild(overlay);
    }, 3000);
}

// Inject a script into the page to access the page's JavaScript context
function injectPageScript() {
    try {
        // Create a script element to load our external script
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('pageScript.js');
        script.onload = function() {
            // Script has loaded, now we can initialize it
            console.log('Page script loaded');

            // Send a message to initialize the script
            window.postMessage({
                action: 'initializePocketOptionBot'
            }, '*');
        };

        // Append the script to the page
        document.head.appendChild(script);
        console.log('Injected page script');

        // Listen for events from the injected script
        window.addEventListener('message', function(event) {
            // Only accept messages from the same frame
            if (event.source !== window) return;

            const message = event.data;

            // Check if this is a message from our page script
            if (message && message.from === 'pocketOptionBot') {
                if (message.action === 'botReady') {
                    console.log('Page script is ready');
                }
                else if (message.action === 'balanceUpdated') {
                    console.log('Balance updated from page script:', message.balance);
                    if (message.balance && !isNaN(parseFloat(message.balance))) {
                        currentBalance = parseFloat(message.balance);
                        updateFloatingInterfaceBalance(currentBalance);
                    }
                }
                else if (message.action === 'buttonsFound') {
                    console.log('Trading buttons found:', message.buttons);
                }
                else if (message.action === 'tradeExecuted') {
                    console.log('Trade execution result:', message);
                    if (message.success) {
                        showNotification(`${message.direction} trade executed for $${message.amount}`, 'success');
                    } else {
                        showNotification(`Failed to execute trade: ${message.error}`, 'error');
                    }
                }
                else if (message.action === 'balanceFound') {
                    console.log('Balance found from page script:', message.balance);
                    if (message.balance && !isNaN(parseFloat(message.balance))) {
                        currentBalance = parseFloat(message.balance);
                        updateFloatingInterfaceBalance(currentBalance);
                    }
                }
            }
        });

    } catch (e) {
        console.error('Error injecting page script:', e);
    }
}

// Get balance directly from the page context
function getBalanceFromPage() {
    try {
        console.log('Getting balance from page context');

        // Send a message to the page script to get the balance
        window.postMessage({
            action: 'initializePocketOptionBot'
        }, '*');

        // Also try to directly extract balance from DOM
        setTimeout(() => {
            // This is a fallback in case the script approach doesn't work
            findBalanceElementDeepScan();
        }, 2000);

    } catch (e) {
        console.error('Error getting balance from page:', e);

        // Fallback to DOM scanning
        findBalanceElementDeepScan();
    }
}

// Find trading buttons on the page
function findTradingButtons() {
    try {
        // Create a temporary script to find the buttons
        const script = document.createElement('script');
        script.textContent = `
            try {
                const buttons = window.pocketOptionBot.findTradingButtons();
                if (buttons) {
                    window.dispatchEvent(new CustomEvent('pocketOptionBotButtonsGet', {
                        detail: { buttons: buttons }
                    }));
                }
            } catch (e) {
                console.error('Error finding trading buttons from page:', e);
            }
        `;

        // Add listener for the event
        window.addEventListener('pocketOptionBotButtonsGet', function(event) {
            console.log('Got trading buttons from page script:', event.detail.buttons);
        }, { once: true }); // Only listen once

        // Execute the script
        document.head.appendChild(script);
        script.remove(); // Clean up

    } catch (e) {
        console.error('Error finding trading buttons from page:', e);
    }
}

// Scan for trading elements (buttons and inputs)
function scanForTradingElements() {
    console.log('Scanning for trading elements...');

    // Find all buttons on the page
    const allButtons = document.querySelectorAll('button, a.btn, .btn, [class*="btn-"], [role="button"]');
    console.log(`Found ${allButtons.length} potential button elements on the page`);

    // Find all inputs on the page - first try Pocket Option specific selectors
    const pocketOptionInputs = document.querySelectorAll('.block--bet-amount .value__val input, .block--bet-amount input, .control__value input, .value__val input');
    console.log(`Found ${pocketOptionInputs.length} Pocket Option specific amount inputs`);

    // If Pocket Option specific inputs found, use those
    let amountInputs = Array.from(pocketOptionInputs);

    // If no Pocket Option specific inputs found, try looking for inputs near "Amount" text
    if (amountInputs.length === 0) {
        console.log('No Pocket Option specific inputs found, looking for inputs near "Amount" text');

        // Find elements containing "Amount" text
        const amountLabels = document.querySelectorAll('div:contains("Amount"), span:contains("Amount"), label:contains("Amount"), .block__title:contains("Amount")');
        console.log(`Found ${amountLabels.length} elements containing "Amount" text`);

        // Look for inputs near these elements
        for (const label of amountLabels) {
            // Check the parent element for inputs
            const parent = label.parentElement;
            if (parent) {
                const nearbyInputs = parent.querySelectorAll('input');
                if (nearbyInputs.length > 0) {
                    console.log(`Found ${nearbyInputs.length} inputs near "Amount" text`);
                    amountInputs = Array.from(nearbyInputs);
                    break;
                }

                // Also check siblings of the parent
                const parentParent = parent.parentElement;
                if (parentParent) {
                    const siblings = parentParent.children;
                    for (let i = 0; i < siblings.length; i++) {
                        if (siblings[i] !== parent) {
                            const siblingInputs = siblings[i].querySelectorAll('input');
                            if (siblingInputs.length > 0) {
                                console.log(`Found ${siblingInputs.length} inputs in sibling of "Amount" text container`);
                                amountInputs = Array.from(siblingInputs);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    // If still no inputs found, try generic selectors
    if (amountInputs.length === 0) {
        console.log('No inputs found near "Amount" text, trying generic selectors');
        const genericAmountInputs = document.querySelectorAll('input[type="number"], input.amount, input[placeholder*="amount"], input[name*="amount"], .amount-input, [data-role="amount-input"]');
        console.log(`Found ${genericAmountInputs.length} generic amount inputs`);
        amountInputs = Array.from(genericAmountInputs);
    }

    // If still no inputs found, try even more generic selectors
    if (amountInputs.length === 0) {
        console.log('No specific amount inputs found, trying more generic selectors');
        const genericInputs = document.querySelectorAll('input[type="text"], input[type="tel"], input:not([type="hidden"])');
        console.log(`Found ${genericInputs.length} generic input elements`);

        // Score each input based on likelihood of being the amount input
        const scoredInputs = Array.from(genericInputs).map(input => {
            let score = 0;

            // Check input attributes
            if (input.type === 'number') score += 5;
            if (input.name?.toLowerCase().includes('amount')) score += 10;
            if (input.id?.toLowerCase().includes('amount')) score += 10;
            if (input.placeholder?.toLowerCase().includes('amount')) score += 10;
            if (input.className?.toLowerCase().includes('amount')) score += 8;

            // Check parent elements for amount-related text
            let parent = input.parentElement;
            for (let i = 0; i < 3 && parent; i++) { // Check up to 3 levels up
                const text = parent.textContent?.toLowerCase() || '';
                if (text.includes('amount')) score += 10 - (i * 2);
                if (text.includes('bet')) score += 8 - (i * 2);
                if (text.includes('trade')) score += 6 - (i * 2);
                if (text.includes('$') || text.includes('usd')) score += 5 - (i * 2);
                parent = parent.parentElement;
            }

            // Check if the input has a numeric value
            const value = input.value;
            if (value && !isNaN(parseFloat(value))) score += 5;

            return { input, score };
        });

        // Sort by score (highest first) and take inputs with score > 0
        scoredInputs.sort((a, b) => b.score - a.score);
        amountInputs = scoredInputs.filter(item => item.score > 0).map(item => item.input);
        console.log(`Scored and filtered to ${amountInputs.length} likely amount inputs`);

        // If no inputs scored > 0, just take all inputs as a last resort
        if (amountInputs.length === 0 && genericInputs.length > 0) {
            console.log('No inputs scored > 0, using all generic inputs as a last resort');
            amountInputs = Array.from(genericInputs);
        }
    }

    // Log details about the found inputs
    if (amountInputs.length > 0) {
        console.log('Amount input details:');
        amountInputs.slice(0, 3).forEach((input, index) => {
            console.log(`Input ${index + 1}:`, {
                tagName: input.tagName,
                type: input.type,
                className: input.className,
                id: input.id,
                placeholder: input.placeholder,
                value: input.value,
                parentText: input.parentElement?.textContent?.trim().substring(0, 50)
            });
        });
    }

    // Look for Pocket Option specific button structure based on the HTML provided
    // First try to find the specific button container
    const buttonContainer = document.querySelector('.tour-action-buttons-container, .action-buttons-container');
    let buyButtons = [];
    let sellButtons = [];

    if (buttonContainer) {
        console.log('Found Pocket Option button container:', buttonContainer);

        // Look for buy/call button within the container
        const buyButton = buttonContainer.querySelector('.btn-call, .button-call-wrap a, a.btn-call, [class*="btn-call"], [class*="buy"]');
        if (buyButton) {
            console.log('Found Pocket Option buy button:', buyButton);
            buyButtons.push(buyButton);
        }

        // Look for sell/put button within the container
        const sellButton = buttonContainer.querySelector('.btn-put, .button-put-wrap a, a.btn-put, [class*="btn-put"], [class*="sell"]');
        if (sellButton) {
            console.log('Found Pocket Option sell button:', sellButton);
            sellButtons.push(sellButton);
        }
    }

    // If we didn't find the specific buttons, try more generic selectors
    if (buyButtons.length === 0 && sellButtons.length === 0) {
        console.log('No specific Pocket Option buttons found, trying generic selectors');

        // Find potential buy buttons
        buyButtons = Array.from(allButtons).filter(button => {
            const text = button.textContent?.toLowerCase() || '';
            const classes = button.className?.toLowerCase() || '';
            const style = window.getComputedStyle(button);
            const backgroundColor = style.backgroundColor;

            // Check for SVG with buy/call text
            const hasBuySvg = button.querySelector('svg[data-src*="buy"], svg[data-src*="call"]') !== null;

            return text.includes('buy') || text.includes('call') || text.includes('up') ||
                   classes.includes('buy') || classes.includes('call') || classes.includes('up') ||
                   backgroundColor.includes('green') || backgroundColor.includes('rgb(0, 128') ||
                   backgroundColor.includes('rgb(0, 255') || hasBuySvg;
        });

        // Find potential sell buttons
        sellButtons = Array.from(allButtons).filter(button => {
            const text = button.textContent?.toLowerCase() || '';
            const classes = button.className?.toLowerCase() || '';
            const style = window.getComputedStyle(button);
            const backgroundColor = style.backgroundColor;

            // Check for SVG with sell/put text
            const hasSellSvg = button.querySelector('svg[data-src*="sell"], svg[data-src*="put"]') !== null;

            return text.includes('sell') || text.includes('put') || text.includes('down') ||
                   classes.includes('sell') || classes.includes('put') || classes.includes('down') ||
                   backgroundColor.includes('red') || backgroundColor.includes('rgb(255, 0') ||
                   backgroundColor.includes('rgb(128, 0') || hasSellSvg;
        });
    }

    console.log(`Found ${buyButtons.length} potential buy buttons`);
    console.log(`Found ${sellButtons.length} potential sell buttons`);
    console.log(`Found ${amountInputs.length} potential amount inputs`);

    // Store the found elements for later use
    window.pocketOptionBotElements = {
        buyButtons: buyButtons,
        sellButtons: sellButtons,
        amountInputs: amountInputs
    };

    // Show a notification with the results
    showNotification(`Found ${buyButtons.length} buy buttons, ${sellButtons.length} sell buttons, and ${amountInputs.length} amount inputs`, 'info');

    // Highlight the found elements
    if (buyButtons.length > 0) {
        highlightElement(buyButtons[0], '#00ff00', 5000);
        console.log('Buy button details:', {
            tagName: buyButtons[0].tagName,
            className: buyButtons[0].className,
            id: buyButtons[0].id,
            textContent: buyButtons[0].textContent.trim(),
            html: buyButtons[0].outerHTML.substring(0, 100) + '...'
        });
    }

    if (sellButtons.length > 0) {
        highlightElement(sellButtons[0], '#ff0000', 5000);
        console.log('Sell button details:', {
            tagName: sellButtons[0].tagName,
            className: sellButtons[0].className,
            id: sellButtons[0].id,
            textContent: sellButtons[0].textContent.trim(),
            html: sellButtons[0].outerHTML.substring(0, 100) + '...'
        });
    }

    if (amountInputs.length > 0) {
        highlightElement(amountInputs[0], '#0000ff', 5000);
        console.log('Amount input details:', {
            tagName: amountInputs[0].tagName,
            type: amountInputs[0].type,
            className: amountInputs[0].className,
            id: amountInputs[0].id,
            placeholder: amountInputs[0].placeholder,
            value: amountInputs[0].value
        });
    }

    return window.pocketOptionBotElements;
}

// Parse balance string to number
function parseBalance(balanceStr) {
    // Remove currency symbols and commas, then parse as float
    return parseFloat(balanceStr.replace(/[^0-9.-]+/g, ''));
}

// Highlight an element temporarily
function highlightElement(element, color = '#00ff00', duration = 5000) {
    if (!element) return;

    console.log('Highlighting element:', element, 'with color:', color, 'for duration:', duration);

    // Store original styles
    const originalStyles = {
        border: element.style.border,
        backgroundColor: element.style.backgroundColor,
        boxShadow: element.style.boxShadow,
        transition: element.style.transition,
        outline: element.style.outline,
        position: element.style.position,
        zIndex: element.style.zIndex
    };

    // Apply highlight styles
    element.style.border = `3px solid ${color}`;
    element.style.backgroundColor = `${color}33`; // 20% opacity
    element.style.boxShadow = `0 0 10px ${color}, 0 0 20px ${color}`;
    element.style.transition = 'all 0.3s ease';
    element.style.outline = `2px dashed ${color}`;

    // Ensure the element is visible
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'static') {
        element.style.position = 'relative';
    }

    // Only set z-index if it's not already set to a high value
    if (!computedStyle.zIndex || parseInt(computedStyle.zIndex) < 1000) {
        element.style.zIndex = '9999';
    }

    // Create a label to show what this element is
    try {
        const label = document.createElement('div');
        label.textContent = element.tagName + (element.className ? ` (${element.className.split(' ')[0]})` : '');
        label.style.position = 'absolute';
        label.style.top = '-25px';
        label.style.left = '0';
        label.style.backgroundColor = color;
        label.style.color = '#fff';
        label.style.padding = '2px 5px';
        label.style.borderRadius = '3px';
        label.style.fontSize = '12px';
        label.style.fontFamily = 'Arial, sans-serif';
        label.style.zIndex = '10000';
        label.style.pointerEvents = 'none';

        // Add the label to the element if it has a non-static position
        if (computedStyle.position !== 'static') {
            element.appendChild(label);
        }
    } catch (e) {
        console.error('Error adding label to highlighted element:', e);
    }

    // Restore original styles after duration
    setTimeout(() => {
        element.style.border = originalStyles.border;
        element.style.backgroundColor = originalStyles.backgroundColor;
        element.style.boxShadow = originalStyles.boxShadow;
        element.style.transition = originalStyles.transition;
        element.style.outline = originalStyles.outline;
        element.style.position = originalStyles.position;
        element.style.zIndex = originalStyles.zIndex;

        // Remove any labels we added
        const labels = element.querySelectorAll('div');
        labels.forEach(label => {
            if (label.style.pointerEvents === 'none' &&
                label.textContent.includes(element.tagName)) {
                try {
                    element.removeChild(label);
                } catch (e) {
                    // Ignore errors when removing labels
                }
            }
        });

        console.log('Highlight removed from element');
    }, duration);
}

// Set up observer for balance changes
function setupBalanceObserver() {
    // First try to find the balance using our selectors
    let balanceElement = findElement(pocketOptionSelectors.balanceSelector);

    // If that fails, try a more aggressive approach to find the balance
    if (!balanceElement) {
        console.log('Standard selectors failed to find balance, trying deep scan...');
        balanceElement = findBalanceElementDeepScan();
    }

    if (balanceElement) {
        console.log('Found balance element:', balanceElement);

        // Initial balance reading
        currentBalance = parseBalance(balanceElement.textContent);
        console.log('Initial balance:', currentBalance);

        // Update the floating interface with the current balance
        updateFloatingInterfaceBalance(currentBalance);

        // Set up mutation observer to detect balance changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'characterData' || mutation.type === 'childList') {
                    const newBalance = parseBalance(balanceElement.textContent);

                    if (newBalance !== currentBalance && !isNaN(newBalance)) {
                        console.log('Balance changed:', currentBalance, '->', newBalance);
                        currentBalance = newBalance;

                        // Update the floating interface
                        updateFloatingInterfaceBalance(currentBalance);

                        // Notify popup of balance change
                        chrome.runtime.sendMessage({
                            action: 'updateBalance',
                            balance: currentBalance
                        });
                    }
                }
            });
        });

        observer.observe(balanceElement, {
            characterData: true,
            childList: true,
            subtree: true
        });

        console.log('Balance observer set up');

        // Also set up a periodic check for balance changes and deep scan
        // This helps in case the mutation observer misses some changes
        setInterval(() => {
            // First check the known balance element
            if (balanceElement) {
                const newBalance = parseBalance(balanceElement.textContent);
                if (newBalance !== currentBalance && !isNaN(newBalance)) {
                    console.log('Balance changed (interval check):', currentBalance, '->', newBalance);
                    currentBalance = newBalance;

                    // Update the floating interface
                    updateFloatingInterfaceBalance(currentBalance);

                    // Notify popup of balance change
                    chrome.runtime.sendMessage({
                        action: 'updateBalance',
                        balance: currentBalance
                    });
                }
            }

            // Also do a deep scan periodically to make sure we have the right element
            const newBalanceElement = findBalanceElementDeepScan();
            if (newBalanceElement && newBalanceElement !== balanceElement) {
                console.log('Found new balance element in deep scan');
                balanceElement = newBalanceElement;

                const newBalance = parseBalance(balanceElement.textContent);
                if (newBalance !== currentBalance && !isNaN(newBalance)) {
                    console.log('Balance updated from new element:', currentBalance, '->', newBalance);
                    currentBalance = newBalance;

                    // Update the floating interface
                    updateFloatingInterfaceBalance(currentBalance);

                    // Set up a new observer for this element
                    observer.disconnect();
                    observer.observe(balanceElement, {
                        characterData: true,
                        childList: true,
                        subtree: true
                    });
                }
            }
        }, 5000); // Check every 5 seconds
    } else {
        console.error('Could not find balance element for observation');

        // Try to find balance element periodically with deep scan
        const balanceCheckInterval = setInterval(() => {
            const newBalanceElement = findBalanceElementDeepScan();
            if (newBalanceElement) {
                console.log('Found balance element after deep scan retry');
                clearInterval(balanceCheckInterval);
                balanceElement = newBalanceElement;

                // Get the balance
                currentBalance = parseBalance(balanceElement.textContent);
                console.log('Found balance:', currentBalance);

                // Update the floating interface
                updateFloatingInterfaceBalance(currentBalance);

                // Set up the observer
                setupBalanceObserver();
            }
        }, 5000); // Check every 5 seconds
    }
}

// Deep scan the page for balance elements
function findBalanceElementDeepScan() {
    console.log('Performing deep scan for balance element...');

    // Common balance-related keywords and patterns
    const balanceKeywords = ['balance', 'account', 'funds', 'wallet', 'deposit', 'money', 'cash', 'total'];
    const currencySymbols = ['$', '€', '£', '¥', '₹', '₽', '₴', '₿', 'USD', 'EUR', 'GBP'];

    // Create a visual overlay to show we're scanning for balance
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
    overlay.style.zIndex = '9999998';
    overlay.style.pointerEvents = 'none';
    document.body.appendChild(overlay);

    // First, try to find elements with specific balance-related attributes
    const specificBalanceElements = [
        ...document.querySelectorAll('[data-balance]'),
        ...document.querySelectorAll('[data-account-balance]'),
        ...document.querySelectorAll('[data-value]'),
        ...document.querySelectorAll('[data-amount]'),
        ...document.querySelectorAll('[data-hd-show]'),
        ...document.querySelectorAll('.js-balance'),
        ...document.querySelectorAll('.js-balance-demo'),
        ...document.querySelectorAll('.js-hd')
    ];

    console.log(`Found ${specificBalanceElements.length} elements with specific balance attributes`);

    // Check each element for a potential balance value
    for (const element of specificBalanceElements) {
        // Check attributes
        const balanceValue = element.getAttribute('data-balance') ||
                           element.getAttribute('data-account-balance') ||
                           element.getAttribute('data-value') ||
                           element.getAttribute('data-amount') ||
                           element.getAttribute('data-hd-show');

        if (balanceValue && balanceValue !== '*******') {
            // Try to parse as a number
            const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
            if (!isNaN(parsedBalance)) {
                console.log('Found balance from attribute:', parsedBalance, 'Element:', element);

                // Highlight the element
                element.style.border = '3px solid #00ff00';
                element.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';

                // Don't show notification for balance
                // showNotification(`Found balance: $${parsedBalance.toFixed(2)}`, 'success');

                // Update the balance
                currentBalance = parsedBalance;
                updateFloatingInterfaceBalance(currentBalance, true);

                // Remove overlay
                setTimeout(() => {
                    document.body.removeChild(overlay);
                }, 3000);

                return element;
            }
        }

        // Check text content
        const text = element.textContent.trim();
        if (text && text !== '*******' && /[\d,.]+/.test(text)) {
            // Try to parse as a number
            const parsedBalance = parseFloat(text.replace(/,/g, ''));
            if (!isNaN(parsedBalance)) {
                console.log('Found balance from text content:', parsedBalance, 'Element:', element);

                // Highlight the element
                element.style.border = '3px solid #00ff00';
                element.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';

                // Don't show notification for balance
                // showNotification(`Found balance: $${parsedBalance.toFixed(2)}`, 'success');

                // Update the balance
                currentBalance = parsedBalance;
                updateFloatingInterfaceBalance(currentBalance, true);

                // Remove overlay
                setTimeout(() => {
                    document.body.removeChild(overlay);
                }, 3000);

                return element;
            }
        }
    }

    // Continue with the original deep scan approach
    // First look for elements with balance-related text
    const allElements = document.querySelectorAll('*');
    let bestCandidate = null;
    let highestScore = 0;
    let allCandidates = [];

    // First pass: look for elements with specific data attributes
    const dataAttributeElements = document.querySelectorAll('[data-balance], [data-account-balance], [data-value], [data-amount]');
    if (dataAttributeElements.length > 0) {
        console.log(`Found ${dataAttributeElements.length} elements with balance-related data attributes`);
        for (const element of dataAttributeElements) {
            const balanceValue = element.getAttribute('data-balance') ||
                               element.getAttribute('data-account-balance') ||
                               element.getAttribute('data-value') ||
                               element.getAttribute('data-amount');

            if (balanceValue && !isNaN(parseFloat(balanceValue))) {
                console.log('Found balance from data attribute:', parseFloat(balanceValue));

                // Highlight this element
                highlightElement(element, '#00ff00');

                // Don't show notification for balance
                // showNotification(`Found balance: $${parseFloat(balanceValue)}`, 'success');

                // Update the balance
                currentBalance = parseFloat(balanceValue);
                updateFloatingInterfaceBalance(currentBalance);

                // Remove overlay
                setTimeout(() => {
                    document.body.removeChild(overlay);
                }, 3000);

                return element;
            }
        }
    }

    // Second pass: scan all elements
    for (const element of allElements) {
        // Skip script, style, and other non-visible elements
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE' ||
            element.tagName === 'META' || element.tagName === 'LINK' ||
            element.tagName === 'HTML' || element.tagName === 'HEAD' ||
            element.tagName === 'BODY') {
            continue;
        }

        // Skip elements with too many children
        if (element.children.length > 5) {
            continue;
        }

        // Get the text content
        const text = element.textContent.trim();

        // Skip empty elements or elements with too much text
        if (!text || text.length > 50) {
            continue;
        }

        // Check if the text contains a number with currency symbol
        let hasNumber = /\d+(\.\d+)?/.test(text);
        let hasCurrencySymbol = currencySymbols.some(symbol => text.includes(symbol));

        // Check if the element or its parent has balance-related class or ID
        const elementClasses = element.className ? element.className.toLowerCase() : '';
        const elementId = element.id ? element.id.toLowerCase() : '';
        const parentClasses = element.parentElement ? element.parentElement.className.toLowerCase() : '';
        const parentId = element.parentElement ? element.parentElement.id.toLowerCase() : '';

        let hasBalanceClass = balanceKeywords.some(keyword =>
            elementClasses.includes(keyword) || elementId.includes(keyword) ||
            parentClasses.includes(keyword) || parentId.includes(keyword)
        );

        // Calculate a score for this element
        let score = 0;
        if (hasNumber) score += 5;
        if (hasCurrencySymbol) score += 10;
        if (hasBalanceClass) score += 15;

        // Additional checks for balance-related text
        for (const keyword of balanceKeywords) {
            if (text.toLowerCase().includes(keyword)) {
                score += 5;
            }
        }

        // Check if this element is visible
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            score -= 20; // Penalize hidden elements
        }

        // If this element has a decent score, add it to candidates
        if (score > 5) {
            allCandidates.push({
                element,
                score,
                text
            });

            // Temporarily highlight this element
            const originalBorder = element.style.border;
            element.style.border = '1px solid yellow';
            setTimeout(() => {
                element.style.border = originalBorder;
            }, 3000);
        }

        // If this element has a higher score than our current best, update it
        if (score > highestScore) {
            highestScore = score;
            bestCandidate = element;
            console.log('New best balance candidate:', element, 'Score:', score, 'Text:', text);
        }
    }

    // Sort candidates by score
    allCandidates.sort((a, b) => b.score - a.score);

    // Log top candidates
    console.log('Top balance candidates:');
    for (let i = 0; i < Math.min(5, allCandidates.length); i++) {
        console.log(`Candidate ${i+1}: Score ${allCandidates[i].score}, Text: ${allCandidates[i].text}`);
    }

    // If we found a good candidate, highlight it and return it
    if (bestCandidate && highestScore > 10) {
        console.log('Found balance element through deep scan:', bestCandidate, 'Score:', highestScore);

        // Highlight this element
        highlightElement(bestCandidate, '#00ff00');

        // Try to extract the balance value
        const text = bestCandidate.textContent.trim();
        const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
        if (matches && matches[1]) {
            const balanceValue = parseFloat(matches[1].replace(',', '.'));
            if (!isNaN(balanceValue)) {
                console.log('Extracted balance value:', balanceValue);

                // Don't show notification for balance
                // showNotification(`Found balance: $${balanceValue}`, 'success');

                // Update the balance
                currentBalance = balanceValue;
                updateFloatingInterfaceBalance(currentBalance);
            }
        }

        // Remove overlay
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 3000);

        return bestCandidate;
    }

    // If we didn't find a good candidate, try a different approach
    // Look for elements that contain only a number with currency symbol
    for (const element of allElements) {
        const text = element.textContent.trim();

        // Skip empty elements or elements with too much text
        if (!text || text.length > 30) {
            continue;
        }

        // Check if the text is a number with currency symbol and little else
        const hasNumber = /\d+(\.\d+)?/.test(text);
        const hasCurrencySymbol = currencySymbols.some(symbol => text.includes(symbol));
        const isClean = /^[\s\d.,+\-$€£¥₹₽₴₿]+$/.test(text);

        if (hasNumber && (hasCurrencySymbol || isClean)) {
            console.log('Found potential balance element (second pass):', element, 'Text:', text);

            // Highlight this element
            highlightElement(element, '#00ff00');

            // Try to extract the balance value
            const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
            if (matches && matches[1]) {
                const balanceValue = parseFloat(matches[1].replace(',', '.'));
                if (!isNaN(balanceValue)) {
                    console.log('Extracted balance value:', balanceValue);

                    // Don't show notification for balance
                    // showNotification(`Found balance: $${balanceValue}`, 'success');

                    // Update the balance
                    currentBalance = balanceValue;
                    updateFloatingInterfaceBalance(currentBalance);
                }
            }

            // Remove overlay
            setTimeout(() => {
                document.body.removeChild(overlay);
            }, 3000);

            return element;
        }
    }

    // If all else fails, try to find the balance by looking at the DOM structure
    // Many trading platforms have the balance in a header or sidebar
    const headerElements = document.querySelectorAll('header, .header, #header, [class*="header"]');
    for (const header of headerElements) {
        const allHeaderElements = header.querySelectorAll('*');
        for (const element of allHeaderElements) {
            const text = element.textContent.trim();
            if (text && text.length < 30) {
                const hasNumber = /\d+(\.\d+)?/.test(text);
                const hasCurrencySymbol = currencySymbols.some(symbol => text.includes(symbol));

                if (hasNumber && hasCurrencySymbol) {
                    console.log('Found potential balance in header:', element, 'Text:', text);

                    // Don't highlight the element

                    // Try to extract the balance value
                    const matches = text.match(/[\$€£¥₹₽₴₿]?\s*(\d+(?:[.,]\d+)?)/);
                    if (matches && matches[1]) {
                        const balanceValue = parseFloat(matches[1].replace(',', '.'));
                        if (!isNaN(balanceValue)) {
                            console.log('Extracted balance value from header:', balanceValue);

                            // Don't show notification

                            // Update the balance
                            currentBalance = balanceValue;
                            updateFloatingInterfaceBalance(currentBalance);
                        }
                    }

                    // Remove overlay
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                    }, 3000);

                    return element;
                }
            }
        }
    }

    // Remove overlay if we didn't find anything
    setTimeout(() => {
        document.body.removeChild(overlay);
    }, 3000);

    console.log('Deep scan failed to find balance element');
    return null;
}

// Function to update the floating interface with the current balance
function updateFloatingInterfaceBalance(balance, isRealBalance = true) {
    if (floatingFrame && floatingFrame.contentWindow) {
        try {
            // Ensure the balance is a valid number
            let numericBalance = balance;
            if (typeof balance === 'string') {
                // Remove any commas and convert to number
                numericBalance = parseFloat(balance.replace(/,/g, ''));
            }

            // Validate the balance is a number
            if (isNaN(numericBalance)) {
                console.error('Invalid balance value:', balance);
                return;
            }

            // Log the balance we're sending to the floating interface
            console.log('Sending balance update to floating interface:', {
                numericBalance,
                originalBalance: balance,
                isRealBalance
            });

            // Check if this is the first time we're detecting a balance
            const isFirstBalance = !localStorage.getItem('pocketOptionBalance');

            // Store the balance in localStorage for persistence
            try {
                localStorage.setItem('pocketOptionBalance', numericBalance.toString());
                console.log('Saved balance to localStorage:', numericBalance);
            } catch (e) {
                console.error('Failed to save balance to localStorage:', e);
            }

            // Add a timestamp to ensure the message is treated as new
            floatingFrame.contentWindow.postMessage({
                action: 'updateBalance',
                balance: numericBalance,
                originalBalance: balance, // Send the original value for debugging
                realBalance: isRealBalance, // Flag to indicate if this is the real balance from the site
                timestamp: Date.now() // Add timestamp to ensure the message is treated as new
            }, '*');

            // Only show a connection notification, but don't mention the balance
            if (isFirstBalance && isRealBalance) {
                // Show a notification about the connection only
                showNotification(`Connected to Pocket Option`, 'success');

                // Store the balance in localStorage
                try {
                    localStorage.setItem('pocketOptionBalance', numericBalance.toString());
                    localStorage.setItem('pocketOptionBalanceTimestamp', Date.now().toString());
                } catch (e) {
                    console.error('Failed to save balance to localStorage:', e);
                }
            }
        } catch (error) {
            console.error('Error sending balance update to floating interface:', error);
        }
    }
}

// Set up observer for trade results
function setupTradeObserver() {
    const tradeHistoryContainer = findElement(pocketOptionSelectors.tradeHistoryContainer);

    if (tradeHistoryContainer) {
        console.log('Found trade history container:', tradeHistoryContainer);

        // Set up mutation observer to detect new trade results
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // New trade result added
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];

                        // Check if this is a trade item
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Try to find the result indicator within this node
                            let tradeResult = null;

                            // If the node itself is a trade item
                            if (Array.isArray(pocketOptionSelectors.tradeItems)) {
                                for (const selector of pocketOptionSelectors.tradeItems) {
                                    if (node.matches(selector)) {
                                        // This node is a trade item, look for result inside it
                                        if (Array.isArray(pocketOptionSelectors.tradeResult)) {
                                            for (const resultSelector of pocketOptionSelectors.tradeResult) {
                                                const result = node.querySelector(resultSelector);
                                                if (result) {
                                                    tradeResult = result;
                                                    break;
                                                }
                                            }
                                        } else {
                                            tradeResult = node.querySelector(pocketOptionSelectors.tradeResult);
                                        }
                                        break;
                                    }
                                }
                            } else if (node.matches(pocketOptionSelectors.tradeItems)) {
                                // This node is a trade item, look for result inside it
                                if (Array.isArray(pocketOptionSelectors.tradeResult)) {
                                    for (const resultSelector of pocketOptionSelectors.tradeResult) {
                                        const result = node.querySelector(resultSelector);
                                        if (result) {
                                            tradeResult = result;
                                            break;
                                        }
                                    }
                                } else {
                                    tradeResult = node.querySelector(pocketOptionSelectors.tradeResult);
                                }
                            }

                            // If we found a result, process it
                            if (tradeResult) {
                                // Determine if it's a win or loss
                                const isWin = determineTradeResult(tradeResult);

                                console.log('Trade completed with result:', isWin ? 'WIN' : 'LOSS');

                                // Update martingale sequence based on trade result
                                updateMartingaleIndex(isWin);
                                console.log('Martingale sequence updated. Next trade amount:', getMartingaleAmount());

                                // Notify the floating interface
                                if (floatingFrame && floatingFrame.contentWindow) {
                                    floatingFrame.contentWindow.postMessage({
                                        action: 'tradeResult',
                                        result: isWin ? 'WIN' : 'LOSS',
                                        nextAmount: getMartingaleAmount() // Send next amount to the interface
                                    }, '*');
                                }

                                // Notify popup of trade result
                                chrome.runtime.sendMessage({
                                    action: 'tradeResult',
                                    result: isWin ? 'WIN' : 'LOSS',
                                    nextAmount: getMartingaleAmount() // Send next amount to the popup
                                });

                                // Show notification with next trade amount
                                showNotification(`Trade result: ${isWin ? 'WIN' : 'LOSS'}. Next amount: $${getMartingaleAmount()}`, isWin ? 'success' : 'warning');
                            }
                        }
                    }
                }
            });
        });

        observer.observe(tradeHistoryContainer, {
            childList: true,
            subtree: true
        });

        console.log('Trade observer set up');
        observingTrades = true;

        // Also check for existing trade results
        checkExistingTradeResults();
    } else {
        console.log('Could not find trade history container');

        // Try to find trade history container periodically
        const tradeHistoryCheckInterval = setInterval(() => {
            const tradeHistoryContainer = findElement(pocketOptionSelectors.tradeHistoryContainer);
            if (tradeHistoryContainer) {
                console.log('Found trade history container after retry');
                clearInterval(tradeHistoryCheckInterval);
                setupTradeObserver(); // Recursively call to set up the observer
            }
        }, 5000); // Check every 5 seconds
    }
}

// Check for existing trade results
function checkExistingTradeResults() {
    console.log('Checking for existing trade results...');

    // Find all trade items
    let tradeItems = [];

    if (Array.isArray(pocketOptionSelectors.tradeItems)) {
        for (const selector of pocketOptionSelectors.tradeItems) {
            const items = document.querySelectorAll(selector);
            if (items.length > 0) {
                tradeItems = Array.from(items);
                break;
            }
        }
    } else {
        tradeItems = document.querySelectorAll(pocketOptionSelectors.tradeItems);
    }

    console.log(`Found ${tradeItems.length} existing trade items`);

    // Process each trade item
    for (const item of tradeItems) {
        let tradeResult = null;

        // Find the result element
        if (Array.isArray(pocketOptionSelectors.tradeResult)) {
            for (const selector of pocketOptionSelectors.tradeResult) {
                const result = item.querySelector(selector);
                if (result) {
                    tradeResult = result;
                    break;
                }
            }
        } else {
            tradeResult = item.querySelector(pocketOptionSelectors.tradeResult);
        }

        // If we found a result, log it (but don't notify as these are old trades)
        if (tradeResult) {
            const isWin = determineTradeResult(tradeResult);
            console.log('Found existing trade result:', isWin ? 'WIN' : 'LOSS');
        }
    }
}

// Determine if a trade result element indicates a win or loss
function determineTradeResult(resultElement) {
    // Check various indicators of a win
    const text = resultElement.textContent.toLowerCase();
    const classList = Array.from(resultElement.classList);
    const style = window.getComputedStyle(resultElement);
    const color = style.color || '';

    // Check text content
    if (text.includes('win') || text.includes('profit') || text.includes('+') ||
        text.includes('success') || text.includes('gain')) {
        return true;
    }

    // Check class names
    if (classList.some(c =>
        c.includes('win') || c.includes('profit') || c.includes('success') ||
        c.includes('positive') || c.includes('green'))) {
        return true;
    }

    // Check color (green usually indicates win)
    if (color.includes('rgb(0, 128') || color.includes('rgb(0, 255') ||
        color.includes('#0') || color.includes('green')) {
        return true;
    }

    // If none of the above, it's probably a loss
    return false;
}

// Track the last trade execution time to prevent multiple executions
let lastTradeExecutionTime = 0;
// Track the last trade direction to prevent duplicate trades
let lastTradeDirection = null;
// Track the last trade amount to prevent duplicate trades
let lastTradeAmount = null;
// Debounce period in milliseconds (500ms should be enough to prevent duplicates)
const TRADE_DEBOUNCE_PERIOD = 500;

// Execute a trade with debounce to prevent multiple executions
async function executeTrade(direction, amount, expiry) {
    // Check if this is a duplicate trade request within the debounce period
    const now = Date.now();
    if (now - lastTradeExecutionTime < TRADE_DEBOUNCE_PERIOD &&
        direction === lastTradeDirection &&
        amount === lastTradeAmount) {
        console.log('DUPLICATE TRADE PREVENTED: Ignoring duplicate trade request within debounce period');
        showNotification('Prevented duplicate trade execution', 'warning');
        return false;
    }

    // Update the last trade execution time and details
    lastTradeExecutionTime = now;
    lastTradeDirection = direction;
    lastTradeAmount = amount;

    // If amount is not specified and martingale is enabled, use the martingale amount
    if (!amount && settings.quantumSettings && settings.quantumSettings.useMartingale) {
        amount = getMartingaleAmount();
        console.log('Using martingale amount for trade:', amount);
    }

    console.log('Executing trade:', direction, amount, expiry, 'at time:', new Date().toISOString());
    showNotification(`Executing ${direction} trade`, 'info');

    try {
        // Always scan for trading elements first to ensure we have the latest
        console.log('Scanning for trading elements before execution...');
        const elements = scanForTradingElements();

        // First try the Pocket Option specific approach based on the HTML structure provided
        console.log('Trying Pocket Option specific approach...');

        // Find the button container
        const buttonContainer = document.querySelector('.tour-action-buttons-container, .action-buttons-container');
        if (buttonContainer) {
            console.log('Found Pocket Option button container:', buttonContainer);

            // Find the appropriate button based on direction
            const buttonSelector = direction === 'BUY' ?
                '.btn-call, .button-call-wrap a, a.btn-call' :
                '.btn-put, .button-put-wrap a, a.btn-put';

            const button = buttonContainer.querySelector(buttonSelector);

            if (button) {
                console.log(`Found Pocket Option ${direction} button:`, button);
                highlightElement(button, direction === 'BUY' ? '#00ff00' : '#ff0000', 8000);

                // Find the amount input - try different approaches
                // First look for a specific amount input near the trading area
                let amountInput = document.querySelector('.amount-input, input[name*="amount"], input[placeholder*="amount"]');

                // If not found, look for any input that might be the amount
                if (!amountInput) {
                    const allInputs = document.querySelectorAll('input[type="number"], input[type="text"]:not([type="hidden"])');
                    console.log(`Found ${allInputs.length} potential input elements`);

                    // Try to find the most likely amount input
                    for (const input of allInputs) {
                        const placeholder = input.placeholder?.toLowerCase() || '';
                        const name = input.name?.toLowerCase() || '';
                        const id = input.id?.toLowerCase() || '';
                        const classes = input.className?.toLowerCase() || '';
                        const nearbyText = input.parentElement?.textContent?.toLowerCase() || '';

                        if (placeholder.includes('amount') || name.includes('amount') ||
                            id.includes('amount') || classes.includes('amount') ||
                            nearbyText.includes('amount') || nearbyText.includes('invest') ||
                            nearbyText.includes('$') || nearbyText.includes('usd')) {

                            amountInput = input;
                            break;
                        }
                    }

                    // If still not found, just take the first input as a fallback
                    if (!amountInput && allInputs.length > 0) {
                        amountInput = allInputs[0];
                    }
                }

                // If we found an amount input, set the amount
                if (amountInput) {
                    console.log('Found amount input:', amountInput);
                    highlightElement(amountInput, '#0000ff', 8000);

                    try {
                        console.log('Setting amount to:', amount, 'on element:', amountInput);

                        // Highlight the amount input to show what we're interacting with
                        highlightElement(amountInput, '#0000ff', 5000);

                        // Make sure the input is visible and scrolled into view
                        amountInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await new Promise(resolve => setTimeout(resolve, 500));

                        // Focus the input
                        amountInput.focus();
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // Clear existing value - try multiple approaches
                        // 1. Direct value setting
                        amountInput.value = '';
                        amountInput.dispatchEvent(new Event('input', { bubbles: true }));

                        // 2. Select all and delete
                        amountInput.select();
                        try {
                            document.execCommand('delete');
                        } catch (e) {
                            console.log('execCommand delete failed:', e);
                        }
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // Set new value - try multiple approaches
                        // 1. Direct value setting
                        amountInput.value = amount.toString();
                        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                        amountInput.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('Amount set via direct value assignment:', amountInput.value);

                        // 2. Also try setting it via attribute
                        amountInput.setAttribute('value', amount.toString());

                        // 3. Try using execCommand
                        try {
                            amountInput.select();
                            document.execCommand('insertText', false, amount.toString());
                        } catch (e) {
                            console.log('execCommand insertText failed:', e);
                        }

                        // 4. Try simulating typing if direct setting didn't work
                        if (amountInput.value !== amount.toString()) {
                            console.log('Direct value setting failed, trying to simulate typing');

                            // Clear again
                            amountInput.value = '';
                            amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // Simulate typing
                            await simulateTyping(amountInput, amount.toString());
                            console.log('Amount after simulated typing:', amountInput.value);
                        }

                        // 5. Try using a script in the page context as a last resort
                        if (amountInput.value !== amount.toString()) {
                            console.log('All direct methods failed, trying page script approach');

                            const script = document.createElement('script');
                            script.textContent = `
                                (function() {
                                    try {
                                        // Try to find the amount input using the specific structure
                                        const amountInputs = document.querySelectorAll('.block--bet-amount .value__val input, .block--bet-amount input, .control__value input, .value__val input');
                                        console.log('Found ' + amountInputs.length + ' potential amount inputs via script');

                                        for (let i = 0; i < amountInputs.length; i++) {
                                            const input = amountInputs[i];
                                            console.log('Setting amount on input ' + i);

                                            // Focus and clear
                                            input.focus();
                                            input.value = '';
                                            input.dispatchEvent(new Event('input', { bubbles: true }));

                                            // Set new value
                                            input.value = '${amount}';
                                            input.dispatchEvent(new Event('input', { bubbles: true }));
                                            input.dispatchEvent(new Event('change', { bubbles: true }));
                                        }
                                    } catch(e) {
                                        console.error('Error in amount setting script:', e);
                                    }
                                })();
                            `;
                            document.head.appendChild(script);
                            script.remove();

                            // Wait a moment for the script to execute
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }

                        // Final verification and notification
                        const finalValue = amountInput.value;
                        if (finalValue === amount.toString()) {
                            console.log('Successfully set amount to:', amount);
                            // Don't show notification for setting amount
                            // showNotification(`Set amount to $${amount}`, 'success');
                        } else {
                            console.warn('Could not verify amount was set correctly. Current value:', finalValue, 'Expected:', amount.toString());
                            // Don't show notification for setting amount
                            // showNotification(`Attempted to set amount to $${amount}, but verification failed`, 'warning');
                        }

                        // Wait a moment for the amount to register
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } catch (e) {
                        console.error('Error setting amount:', e);
                        showNotification(`Error setting amount: ${e.message}`, 'error');
                    }
                } else {
                    console.log('No amount input found, will try to click the button directly');
                    showNotification('No amount input found, trying direct button click', 'warning');
                }

                // Now click the button
                try {
                    console.log('Clicking button:', button);

                    // Scroll the button into view if needed
                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // Wait a moment for the scroll to complete
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Try multiple approaches to click the button

                    // CRITICAL FIX: Only click the button once
                    console.log('Clicking button ONCE');
                    button.click();
                    console.log('Direct button click executed');

                    // 4. Try a script-based approach - FIXED to only click ONE button
                    const clickScript = document.createElement('script');
                    clickScript.textContent = `
                        try {
                            // Try to find the button by its class - ONLY CLICK THE FIRST ONE
                            const buttonSelector = '${direction === 'BUY' ? '.btn-call, .button-call-wrap a, a.btn-call' : '.btn-put, .button-put-wrap a, a.btn-put'}';
                            const buttons = document.querySelectorAll(buttonSelector);
                            console.log('Found ' + buttons.length + ' buttons matching selector');

                            // CRITICAL FIX: Only click the first button found
                            if (buttons.length > 0) {
                                console.log('Clicking ONLY the first button found');
                                buttons[0].click();
                                // Exit after clicking one button - don't try to find more
                                return;
                            }

                            // Only as a fallback, try to find by text content
                            const allButtons = document.querySelectorAll('a, button, [role="button"], .btn');
                            for (let i = 0; i < allButtons.length; i++) {
                                const btn = allButtons[i];
                                if (btn.textContent.toLowerCase().includes('${direction.toLowerCase()}')) {
                                    console.log('Found button by text: ' + btn.textContent);
                                    btn.click();
                                    // CRITICAL FIX: Exit after clicking one button
                                    return;
                                }
                            }
                        } catch(e) {
                            console.error('Error in click script:', e);
                        }
                    `;
                    document.head.appendChild(clickScript);
                    clickScript.remove();
                    console.log('Click script executed');

                    showNotification(`Clicked ${direction} button`, 'success');

                    // Wait a moment to see if the trade goes through
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    console.log('Trade execution attempted via Pocket Option specific approach');
                    showNotification(`${direction} trade execution attempted`, 'success');

                    // No overlay to remove in silent mode

                    return true;
                } catch (e) {
                    console.error('Error clicking button:', e);
                    showNotification(`Error clicking button: ${e.message}`, 'error');
                }
            } else {
                console.log(`Pocket Option ${direction} button not found in container`);
            }
        } else {
            console.log('Pocket Option button container not found');
        }

        // If the Pocket Option specific approach failed, try the generic approach
        console.log('Pocket Option specific approach failed, trying generic approach...');

        // Use the elements found during scanning
        if (elements && elements.amountInputs.length > 0) {
            // Get the appropriate button based on direction
            const buttons = direction === 'BUY' ? elements.buyButtons : elements.sellButtons;

            if (buttons.length > 0) {
                const amountInput = elements.amountInputs[0];
                const button = buttons[0];

                console.log('Found trading elements:', {
                    amountInput: {
                        tagName: amountInput.tagName,
                        type: amountInput.type,
                        className: amountInput.className
                    },
                    button: {
                        tagName: button.tagName,
                        className: button.className,
                        textContent: button.textContent.trim()
                    }
                });

                // Highlight the elements we're interacting with
                highlightElement(amountInput, '#0000ff', 8000);
                highlightElement(button, direction === 'BUY' ? '#00ff00' : '#ff0000', 8000);

                // 1. Set amount
                try {
                    console.log('Setting amount to:', amount);

                    // Focus the input
                    amountInput.focus();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Clear the input first
                    amountInput.value = '';
                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Set the amount - try multiple approaches
                    // 1. Direct value setting
                    amountInput.value = amount.toString();
                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Amount set via direct value assignment:', amountInput.value);

                    // 2. Also try setting it via attribute
                    amountInput.setAttribute('value', amount.toString());
                    console.log('Amount set via attribute:', amountInput.getAttribute('value'));

                    // 3. Try simulating typing
                    await simulateTyping(amountInput, amount.toString());
                    console.log('Amount after simulated typing:', amountInput.value);

                    // Verify the amount was set correctly
                    if (amountInput.value !== amount.toString()) {
                        console.warn('Amount verification failed. Expected:', amount.toString(), 'Actual:', amountInput.value);
                        // Try one more time with a different approach
                        await clearAndSetInputValue(amountInput, amount.toString());
                    }

                    // Don't show notification for setting amount
                    // showNotification(`Set amount to $${amount}`, 'info');
                    console.log('Amount set successfully, final value:', amountInput.value);

                    // Wait a moment for the amount to register
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (e) {
                    console.error('Error setting amount:', e);
                    showNotification(`Error setting amount: ${e.message}`, 'error');
                }

                // 2. Click the button
                try {
                    console.log('Clicking button:', button);

                    // Scroll the button into view if needed
                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // Wait a moment for the scroll to complete
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // CRITICAL FIX: Only click the button once
                    console.log('Clicking button ONCE');
                    button.click();
                    console.log('Button clicked');

                    showNotification(`Clicked ${direction} button`, 'success');

                    // Wait a moment to see if the trade goes through
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } catch (e) {
                    console.error('Error clicking button:', e);
                    showNotification(`Error clicking button: ${e.message}`, 'error');
                }

                // No overlay to remove in silent mode

                console.log('Trade execution attempted via generic approach');
                showNotification(`${direction} trade execution attempted`, 'success');
                return true;
            } else {
                console.log('No buttons found for direction:', direction);
            }
        } else {
            console.log('No amount inputs found');
        }

        // Try our new robust approach for setting the amount
        console.log('Trying robust amount setting approach...');
        const amountSetSuccess = await setAmountWithRobustApproach(amount);
        if (amountSetSuccess) {
            console.log('Successfully set amount using robust approach');
            // Don't show notification for setting amount
            // showNotification(`Set amount to $${amount} using enhanced method`, 'success');

            // Now try to find and click the appropriate button
            console.log('Looking for trading button...');
            const buttonSelector = direction === 'BUY' ?
                '.btn-call, .button-call-wrap a, a.btn-call, button.buy-button, button.call-button, button.up-button' :
                '.btn-put, .button-put-wrap a, a.btn-put, button.sell-button, button.put-button, button.down-button';

            const button = document.querySelector(buttonSelector);
            if (button) {
                console.log(`Found ${direction} button:`, button);
                highlightElement(button, direction === 'BUY' ? '#00ff00' : '#ff0000', 5000);

                try {
                    // Scroll the button into view
                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // CRITICAL FIX: Only click the button once
                    console.log('Clicking button ONCE');
                    button.click();
                    console.log('Button clicked');

                    showNotification(`Clicked ${direction} button`, 'success');

                    // No overlay to remove in silent mode

                    return true;
                } catch (e) {
                    console.error('Error clicking button:', e);
                }
            }
        }

        // If the robust approach failed, try the page script approach
        console.log('Robust approach failed, trying page script...');
        const success = await executeTradeViaPageScript(direction, amount);
        if (success) {
            console.log('Trade executed successfully via page script');
            showNotification(`${direction} trade executed via page script`, 'success');

            // No overlay to remove in silent mode

            return true;
        }

        // If we got here, all approaches failed
        throw new Error('All trade execution approaches failed');
    } catch (error) {
        console.error('Error executing trade:', error);
        showNotification(`Error executing trade: ${error.message}`, 'error');

        // No overlay to remove in silent mode

        return false;
    }
}

// Execute a trade via the page script
function executeTradeViaPageScript(direction, amount) {
    return new Promise((resolve) => {
        console.log('Executing trade via page script:', direction, amount);

        try {
            // Create a script element to execute in the page context
            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    try {
                        console.log('Page script executing trade:', '${direction}', ${amount});

                        // First try Pocket Option specific approach
                        const buttonContainer = document.querySelector('.tour-action-buttons-container, .action-buttons-container');
                        if (buttonContainer) {
                            console.log('Found Pocket Option button container');

                            // Find the appropriate button based on direction
                            const buttonSelector = '${direction}' === 'BUY' ?
                                '.btn-call, .button-call-wrap a, a.btn-call' :
                                '.btn-put, .button-put-wrap a, a.btn-put';

                            const button = buttonContainer.querySelector(buttonSelector);

                            if (button) {
                                console.log('Found Pocket Option ${direction} button');

                                // Find the amount input - try different approaches based on the provided HTML structure
                                let amountInput = document.querySelector('.block--bet-amount .value__val input, .block--bet-amount input, .control__value input, .value__val input');

                                // If not found with specific selectors, try more generic approaches
                                if (!amountInput) {
                                    // Try to find by looking for "Amount" text
                                    const amountBlocks = document.querySelectorAll('.block--bet-amount, .block__title:contains("Amount"), div:contains("Amount")');
                                    console.log('Found ' + amountBlocks.length + ' potential amount blocks');

                                    for (const block of amountBlocks) {
                                        // Look for inputs within this block
                                        const inputs = block.querySelectorAll('input');
                                        if (inputs.length > 0) {
                                            amountInput = inputs[0];
                                            console.log('Found amount input in amount block');
                                            break;
                                        }

                                        // If no inputs directly in this block, check siblings and children
                                        const parent = block.parentElement;
                                        if (parent) {
                                            // Check siblings
                                            const siblings = Array.from(parent.children);
                                            for (const sibling of siblings) {
                                                if (sibling !== block) {
                                                    const siblingInputs = sibling.querySelectorAll('input');
                                                    if (siblingInputs.length > 0) {
                                                        amountInput = siblingInputs[0];
                                                        console.log('Found amount input in sibling of amount block');
                                                        break;
                                                    }
                                                }
                                            }

                                            // If still not found, check parent's parent
                                            if (!amountInput && parent.parentElement) {
                                                const parentInputs = parent.parentElement.querySelectorAll('input');
                                                if (parentInputs.length > 0) {
                                                    amountInput = parentInputs[0];
                                                    console.log('Found amount input in parent of amount block');
                                                }
                                            }
                                        }
                                    }
                                }

                                // If still not found, fall back to generic selectors
                                if (!amountInput) {
                                    // Try generic amount-related selectors
                                    amountInput = document.querySelector('.amount-input, input[name*="amount"], input[placeholder*="amount"]');

                                    // If still not found, look for any input that might be the amount
                                    if (!amountInput) {
                                        const allInputs = document.querySelectorAll('input[type="number"], input[type="text"]:not([type="hidden"])');
                                        console.log('Found ' + allInputs.length + ' potential input elements');

                                        // Try to find the most likely amount input
                                        for (const input of allInputs) {
                                            const placeholder = input.placeholder || '';
                                            const name = input.name || '';
                                            const id = input.id || '';
                                            const classes = input.className || '';
                                            const nearbyText = input.parentElement ? input.parentElement.textContent : '';

                                            if (placeholder.toLowerCase().includes('amount') ||
                                                name.toLowerCase().includes('amount') ||
                                                id.toLowerCase().includes('amount') ||
                                                classes.toLowerCase().includes('amount') ||
                                                nearbyText.toLowerCase().includes('amount') ||
                                                nearbyText.toLowerCase().includes('invest') ||
                                                nearbyText.toLowerCase().includes('$') ||
                                                nearbyText.toLowerCase().includes('usd')) {

                                                amountInput = input;
                                                break;
                                            }
                                        }

                                        // If still not found, just take the first input as a fallback
                                        if (!amountInput && allInputs.length > 0) {
                                            amountInput = allInputs[0];
                                        }
                                    }
                                }

                                // If we found an amount input, set the amount
                                if (amountInput) {
                                    console.log('Found amount input');

                                    try {
                                        console.log('Setting amount to: ' + ${amount} + ' on element:', amountInput);

                                        // Focus the input and make sure it's visible
                                        amountInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        amountInput.focus();

                                        // Wait a moment for the focus to take effect
                                        setTimeout(() => {
                                            try {
                                                // Clear the input first - try multiple approaches
                                                // 1. Direct value setting
                                                amountInput.value = '';
                                                amountInput.dispatchEvent(new Event('input', { bubbles: true }));

                                                // 2. Select all and delete
                                                amountInput.select();
                                                try {
                                                    document.execCommand('delete');
                                                } catch (e) {
                                                    console.log('execCommand delete failed:', e);
                                                }

                                                // Set the amount - try multiple approaches
                                                // 1. Direct value setting
                                                amountInput.value = '${amount}';
                                                amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                                                amountInput.dispatchEvent(new Event('change', { bubbles: true }));
                                                console.log('Amount set via direct value assignment:', amountInput.value);

                                                // 2. Also try setting it via attribute
                                                amountInput.setAttribute('value', '${amount}');

                                                // 3. Try using execCommand
                                                try {
                                                    amountInput.select();
                                                    document.execCommand('insertText', false, '${amount}');
                                                } catch (e) {
                                                    console.log('execCommand insertText failed:', e);
                                                }

                                                // 4. Try simulating typing
                                                if (amountInput.value !== '${amount}') {
                                                    console.log('Direct setting failed, trying to simulate typing');

                                                    // Clear again
                                                    amountInput.value = '';
                                                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));

                                                    // Simulate typing character by character
                                                    const amountStr = '${amount}';
                                                    for (let i = 0; i < amountStr.length; i++) {
                                                        const char = amountStr[i];

                                                        // Update value
                                                        amountInput.value += char;

                                                        // Dispatch input event
                                                        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                                                    }

                                                    // Final change event
                                                    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
                                                }

                                                console.log('Final amount value:', amountInput.value);
                                            } catch (innerError) {
                                                console.error('Error in delayed amount setting:', innerError);
                                            }
                                        }, 300);
                                    } catch (e) {
                                        console.error('Error setting amount:', e);
                                    }
                                } else {
                                    console.log('No amount input found, will try to click the button directly');
                                }

                                // Now click the button
                                try {
                                    // Scroll the button into view if needed
                                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });

                                    // Try multiple approaches to click the button

                                    // 1. Direct click
                                    button.click();
                                    console.log('Direct button click executed');

                                    // 2. MouseEvent
                                    button.dispatchEvent(new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    }));
                                    console.log('MouseEvent click dispatched');

                                    // 3. Try to click any child elements that might be the actual clickable part
                                    const childElements = button.querySelectorAll('*');
                                    for (const child of childElements) {
                                        try {
                                            child.click();
                                        } catch (e) {
                                            // Ignore errors on child clicks
                                        }
                                    }
                                    console.log('Clicked ' + childElements.length + ' child elements');

                                    // Signal success
                                    window.dispatchEvent(new CustomEvent('tradeExecuted', {
                                        detail: { success: true, direction: '${direction}', amount: ${amount} }
                                    }));

                                    return; // Exit early if successful
                                } catch (e) {
                                    console.error('Error clicking button:', e);
                                }
                            } else {
                                console.log('Pocket Option ${direction} button not found in container');
                            }
                        } else {
                            console.log('Pocket Option button container not found');
                        }

                        // If Pocket Option specific approach failed, try generic approach
                        console.log('Trying generic approach...');

                        // Try to find the amount input using Pocket Option specific selectors first
                        let amountInput = document.querySelector('.block--bet-amount .value__val input, .block--bet-amount input, .control__value input, .value__val input');

                        if (!amountInput) {
                            // Try to find by looking for "Amount" text
                            const amountBlocks = document.querySelectorAll('.block--bet-amount, .block__title:contains("Amount"), div:contains("Amount")');
                            console.log('Found ' + amountBlocks.length + ' potential amount blocks');

                            for (const block of amountBlocks) {
                                // Look for inputs within this block
                                const inputs = block.querySelectorAll('input');
                                if (inputs.length > 0) {
                                    amountInput = inputs[0];
                                    console.log('Found amount input in amount block');
                                    break;
                                }

                                // If no inputs directly in this block, check siblings and children
                                if (block.parentElement) {
                                    // Check siblings
                                    const siblings = Array.from(block.parentElement.children);
                                    for (const sibling of siblings) {
                                        if (sibling !== block) {
                                            const siblingInputs = sibling.querySelectorAll('input');
                                            if (siblingInputs.length > 0) {
                                                amountInput = siblingInputs[0];
                                                console.log('Found amount input in sibling of amount block');
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // If still not found, try generic selectors
                        if (!amountInput) {
                            // Try generic amount-related selectors
                            amountInput = document.querySelector('input[type="number"], input.amount, input[placeholder*="amount"]');

                            // If still not found, try more generic selectors
                            if (!amountInput) {
                                const allInputs = document.querySelectorAll('input[type="text"], input[type="number"]');
                                console.log('Found ' + allInputs.length + ' potential input elements');

                                // Score each input based on likelihood of being the amount input
                                let bestInput = null;
                                let bestScore = 0;

                                for (const input of allInputs) {
                                    let score = 0;

                                    // Check input attributes
                                    if (input.type === 'number') score += 5;
                                    if (input.name && input.name.toLowerCase().includes('amount')) score += 10;
                                    if (input.id && input.id.toLowerCase().includes('amount')) score += 10;
                                    if (input.placeholder && input.placeholder.toLowerCase().includes('amount')) score += 10;
                                    if (input.className && input.className.toLowerCase().includes('amount')) score += 8;

                                    // Check parent elements for amount-related text
                                    let parent = input.parentElement;
                                    for (let i = 0; i < 3 && parent; i++) { // Check up to 3 levels up
                                        const text = parent.textContent.toLowerCase();
                                        if (text.includes('amount')) score += 10 - (i * 2);
                                        if (text.includes('bet')) score += 8 - (i * 2);
                                        if (text.includes('trade')) score += 6 - (i * 2);
                                        if (text.includes('$') || text.includes('usd')) score += 5 - (i * 2);
                                        parent = parent.parentElement;
                                    }

                                    // Check if the input has a numeric value
                                    const value = input.value;
                                    if (value && !isNaN(parseFloat(value))) score += 5;

                                    // If this input has the highest score so far, remember it
                                    if (score > bestScore) {
                                        bestScore = score;
                                        bestInput = input;
                                    }
                                }

                                if (bestInput && bestScore > 5) {
                                    amountInput = bestInput;
                                    console.log('Found likely amount input with score ' + bestScore);
                                } else if (allInputs.length > 0) {
                                    // Last resort: just use the first input
                                    amountInput = allInputs[0];
                                    console.log('Using first input as fallback');
                                }
                            }
                        }

                        if (amountInput) {
                            console.log('Found amount input, setting value to: ' + ${amount});

                            // Focus the input and make sure it's visible
                            amountInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            amountInput.focus();

                            // Clear the input first - try multiple approaches
                            amountInput.value = '';
                            amountInput.dispatchEvent(new Event('input', { bubbles: true }));

                            // Also try select all + delete
                            amountInput.select();
                            try {
                                document.execCommand('delete');
                            } catch (e) {
                                console.log('execCommand delete failed:', e);
                            }

                            // Set the amount - try multiple approaches
                            // 1. Direct value setting
                            amountInput.value = '${amount}';
                            amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                            amountInput.dispatchEvent(new Event('change', { bubbles: true }));

                            // 2. Also try setting it via attribute
                            amountInput.setAttribute('value', '${amount}');

                            // 3. Try using execCommand
                            try {
                                amountInput.select();
                                document.execCommand('insertText', false, '${amount}');
                            } catch (e) {
                                console.log('execCommand insertText failed:', e);
                            }

                            console.log('Amount set to:', ${amount}, 'Final value:', amountInput.value);
                        } else {
                            console.error('Could not find amount input');
                        }

                        // Try to find the button
                        let button;
                        if ('${direction}' === 'BUY') {
                            button = document.querySelector('button.buy-button, button.call-button, button.up-button, button.green, [class*="buy"], [class*="call"], [class*="up"], a.btn-call');
                        } else {
                            button = document.querySelector('button.sell-button, button.put-button, button.down-button, button.red, [class*="sell"], [class*="put"], [class*="down"], a.btn-put');
                        }

                        if (button) {
                            console.log('Found button via generic approach');

                            // Click the button
                            button.click();
                            console.log('Button clicked');

                            // Also try with MouseEvent
                            button.dispatchEvent(new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            }));
                        } else {
                            console.error('Could not find button');
                        }

                        // Signal completion
                        window.dispatchEvent(new CustomEvent('tradeExecuted', {
                            detail: { success: true, direction: '${direction}', amount: ${amount} }
                        }));
                    } catch (error) {
                        console.error('Error executing trade via page script:', error);

                        // Signal error
                        window.dispatchEvent(new CustomEvent('tradeExecuted', {
                            detail: { success: false, error: error.message }
                        }));
                    }
                })();
            `;

            // Add the script to the page
            document.head.appendChild(script);
            script.remove();

            // Listen for the completion event
            window.addEventListener('tradeExecuted', function(event) {
                console.log('Trade execution result:', event.detail);
                resolve(event.detail.success);
            }, { once: true });

            // Set a timeout in case the event never fires
            setTimeout(() => {
                console.log('Trade execution via page script timed out');
                resolve(false);
            }, 8000);
        } catch (e) {
            console.error('Error setting up page script:', e);
            resolve(false);
        }
    });
}

// Find amount input field using various selectors
function findAmountInput() {
    console.log('Looking for amount input field...');

    // First try Pocket Option specific selectors based on the provided HTML structure
    const pocketOptionSelectors = [
        '.block--bet-amount .value__val input',
        '.block--bet-amount input',
        '.control__value input',
        '.value__val input',
        // Look for inputs near "Amount" text
        '.block__title:contains("Amount") + .block__control input',
        'div:contains("Amount") input[type="text"]'
    ];

    // Try the Pocket Option specific selectors first
    for (const selector of pocketOptionSelectors) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`Found Pocket Option amount input with selector: ${selector}`);
                // Highlight the element to help with debugging
                highlightElement(element, '#00ff00', 5000);
                return element;
            }
        } catch (e) {
            // Some selectors might be invalid, ignore errors
            console.log(`Error with selector ${selector}:`, e.message);
        }
    }

    // If Pocket Option specific selectors failed, try looking for elements with specific attributes
    console.log('Pocket Option specific selectors failed, trying to find by attributes...');

    // Look for any input near text containing "Amount"
    const amountLabels = document.querySelectorAll('div:contains("Amount"), span:contains("Amount"), label:contains("Amount")');
    for (const label of amountLabels) {
        // Look for inputs that are siblings or children of the parent
        const parent = label.parentElement;
        if (parent) {
            const nearbyInputs = parent.querySelectorAll('input');
            if (nearbyInputs.length > 0) {
                console.log('Found input near "Amount" text:', nearbyInputs[0]);
                return nearbyInputs[0];
            }

            // Also check siblings
            const siblings = parent.parentElement?.children || [];
            for (const sibling of siblings) {
                const inputs = sibling.querySelectorAll('input');
                if (inputs.length > 0) {
                    console.log('Found input in sibling of "Amount" text:', inputs[0]);
                    return inputs[0];
                }
            }
        }
    }

    // Try the original generic selectors as fallback
    const genericSelectors = [
        'input[name="amount"]',
        'input.amount-input',
        'input.amount',
        'input[placeholder*="amount"]',
        'input[type="number"]'
    ];

    for (const selector of genericSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`Found amount input with generic selector: ${selector}`);
            return element;
        }
    }

    // If we still can't find it, try a deep scan of the DOM
    console.log('All selectors failed, performing deep DOM scan for amount input...');

    // First look for inputs with specific attributes or nearby text
    const allInputs = document.querySelectorAll('input[type="text"], input[type="number"]');
    console.log(`Found ${allInputs.length} text/number inputs in deep scan`);

    // Score each input based on likelihood of being the amount input
    let bestInput = null;
    let bestScore = 0;

    for (const input of allInputs) {
        let score = 0;

        // Check input attributes
        if (input.type === 'number') score += 5;
        if (input.name?.toLowerCase().includes('amount')) score += 10;
        if (input.id?.toLowerCase().includes('amount')) score += 10;
        if (input.placeholder?.toLowerCase().includes('amount')) score += 10;
        if (input.className?.toLowerCase().includes('amount')) score += 8;

        // Check parent elements for amount-related text
        let parent = input.parentElement;
        for (let i = 0; i < 3 && parent; i++) { // Check up to 3 levels up
            const text = parent.textContent?.toLowerCase() || '';
            if (text.includes('amount')) score += 10 - (i * 2);
            if (text.includes('bet')) score += 8 - (i * 2);
            if (text.includes('trade')) score += 6 - (i * 2);
            if (text.includes('$') || text.includes('usd')) score += 5 - (i * 2);
            parent = parent.parentElement;
        }

        // Check if the input has a numeric value
        const value = input.value;
        if (value && !isNaN(parseFloat(value))) score += 5;

        // If this input has the highest score so far, remember it
        if (score > bestScore) {
            bestScore = score;
            bestInput = input;
        }
    }

    if (bestInput && bestScore > 5) {
        console.log(`Found likely amount input in deep scan with score ${bestScore}:`, bestInput);
        return bestInput;
    }

    // Last resort: just return the first input we find
    if (allInputs.length > 0) {
        console.warn('Could not confidently identify amount input, using first input as fallback');
        return allInputs[0];
    }

    console.error('Could not find any input that could be the amount input');
    return null;
}

// Find expiry selector
function findExpirySelector() {
    // Try different possible selectors for the expiry selector
    const possibleSelectors = [
        'select[name="expiry"]',
        '.expiry-selector',
        '.expiry-dropdown',
        'div[data-role="expiry-selector"]'
    ];

    for (const selector of possibleSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`Found expiry selector with selector: ${selector}`);
            return element;
        }
    }

    // If we can't find it with selectors, look for elements that might contain expiry text
    const elements = document.querySelectorAll('div, span, button');
    for (const element of elements) {
        if (element.textContent?.toLowerCase().includes('expiry') ||
            element.textContent?.toLowerCase().includes('expiration')) {
            console.log('Found expiry selector by analyzing DOM');
            return element;
        }
    }

    console.warn('Could not find expiry selector, will use default expiry');
    return null;
}

// Find buy button
function findBuyButton() {
    // Try different possible selectors for the buy button
    const possibleSelectors = [
        'button.buy-button',
        'button[data-action="buy"]',
        'button.call-button',
        'button.up-button',
        'button.higher-button'
    ];

    for (const selector of possibleSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`Found buy button with selector: ${selector}`);
            return element;
        }
    }

    // If we can't find it with selectors, look for buttons with buy-related text or color
    const buttons = document.querySelectorAll('button');
    for (const button of buttons) {
        const text = button.textContent?.toLowerCase() || '';
        const style = window.getComputedStyle(button);
        const backgroundColor = style.backgroundColor;

        if (text.includes('buy') || text.includes('call') || text.includes('up') || text.includes('higher') ||
            backgroundColor.includes('green') || backgroundColor.includes('rgb(0, 128') || backgroundColor.includes('rgb(0, 255')) {
            console.log('Found buy button by analyzing DOM');
            return button;
        }
    }

    console.error('Could not find buy button');
    return null;
}

// Find sell button
function findSellButton() {
    // Try different possible selectors for the sell button
    const possibleSelectors = [
        'button.sell-button',
        'button[data-action="sell"]',
        'button.put-button',
        'button.down-button',
        'button.lower-button'
    ];

    for (const selector of possibleSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`Found sell button with selector: ${selector}`);
            return element;
        }
    }

    // If we can't find it with selectors, look for buttons with sell-related text or color
    const buttons = document.querySelectorAll('button');
    for (const button of buttons) {
        const text = button.textContent?.toLowerCase() || '';
        const style = window.getComputedStyle(button);
        const backgroundColor = style.backgroundColor;

        if (text.includes('sell') || text.includes('put') || text.includes('down') || text.includes('lower') ||
            backgroundColor.includes('red') || backgroundColor.includes('rgb(255, 0') || backgroundColor.includes('rgb(128, 0')) {
            console.log('Found sell button by analyzing DOM');
            return button;
        }
    }

    console.error('Could not find sell button');
    return null;
}

// Select an asset
async function selectAsset(assetName) {
    // Click asset selector button to open dropdown
    await clickElement(pocketOptionSelectors.assetSelectorBtn);

    // Wait for dropdown to appear
    await waitForElement(pocketOptionSelectors.assetDropdown);

    // Find and click the asset
    const assetItems = document.querySelectorAll(pocketOptionSelectors.assetItems);

    for (let i = 0; i < assetItems.length; i++) {
        if (assetItems[i].textContent.includes(assetName)) {
            await clickElement(assetItems[i]);
            return;
        }
    }

    throw new Error(`Asset ${assetName} not found`);
}

// Set trade amount
async function setTradeAmount(amountInput, amount) {
    if (!amountInput) {
        throw new Error('Amount input not found');
    }

    try {
        console.log('Setting trade amount to:', amount, 'on element:', amountInput);

        // Highlight the amount input to show what we're interacting with
        highlightElement(amountInput, '#0000ff', 5000);

        // Make sure the input is visible and scrolled into view
        amountInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await new Promise(resolve => setTimeout(resolve, 300));

        // Focus the input
        amountInput.focus();
        await new Promise(resolve => setTimeout(resolve, 200));

        // IMPROVED APPROACH: Use clipboard to paste the value instead of typing
        // This is more reliable as it completely replaces the content

        // First, try to use the clipboard API to set the amount
        try {
            // Create a script to use the clipboard API in the page context
            const clipboardScript = document.createElement('script');
            clipboardScript.textContent = `
                (function() {
                    try {
                        // Find all potential amount inputs
                        const amountInputs = document.querySelectorAll('.block--bet-amount input, .value__val input, input[type="text"], input[type="number"]');
                        console.log('Found ' + amountInputs.length + ' potential amount inputs for clipboard approach');

                        // Try each input
                        for (let i = 0; i < amountInputs.length; i++) {
                            const input = amountInputs[i];

                            // IMPROVED: Try a more aggressive approach
                            try {
                                // First, try to completely replace the input element
                                const parent = input.parentElement;
                                if (parent) {
                                    // Save the original input properties
                                    const originalType = input.type || 'text';
                                    const originalClass = input.className;
                                    const originalId = input.id;
                                    const originalName = input.name;
                                    const originalPlaceholder = input.placeholder;

                                    // Create a new input with the same properties but our desired value
                                    const newInput = document.createElement('input');
                                    newInput.type = originalType;
                                    newInput.className = originalClass;
                                    newInput.id = originalId;
                                    newInput.name = originalName;
                                    newInput.placeholder = originalPlaceholder;
                                    newInput.value = '${amount}';

                                    // Replace the old input with our new one
                                    parent.replaceChild(newInput, input);

                                    // Dispatch events
                                    newInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    newInput.dispatchEvent(new Event('change', { bubbles: true }));

                                    console.log('Replaced input element with new one containing value: ${amount}');
                                }
                            } catch (replaceError) {
                                console.error('Error replacing input:', replaceError);

                                // If replacement fails, try the clipboard approach
                                try {
                                    // Focus the input
                                    input.focus();

                                    // Select all text
                                    input.select();

                                    // Clear the input by deleting the selection
                                    document.execCommand('delete');

                                    // Use clipboard to set the value
                                    const tempTextarea = document.createElement('textarea');
                                    tempTextarea.value = '${amount}';
                                    document.body.appendChild(tempTextarea);
                                    tempTextarea.select();
                                    document.execCommand('copy');
                                    document.body.removeChild(tempTextarea);

                                    // Focus back on the input
                                    input.focus();

                                    // Paste the value
                                    document.execCommand('paste');

                                    // Dispatch events
                                    input.dispatchEvent(new Event('input', { bubbles: true }));
                                    input.dispatchEvent(new Event('change', { bubbles: true }));

                                    console.log('Amount set via clipboard paste on input ' + i + ':', input.value);
                                } catch (clipboardError) {
                                    console.error('Error using clipboard on input ' + i + ':', clipboardError);

                                    // Last resort: direct value setting
                                    try {
                                        // Clear the input
                                        input.value = '';
                                        input.dispatchEvent(new Event('input', { bubbles: true }));

                                        // Set the new value
                                        input.value = '${amount}';
                                        input.dispatchEvent(new Event('input', { bubbles: true }));
                                        input.dispatchEvent(new Event('change', { bubbles: true }));

                                        console.log('Amount set via direct value assignment on input ' + i + ':', input.value);
                                    } catch (directError) {
                                        console.error('Error setting value directly on input ' + i + ':', directError);
                                    }
                                }
                            }
                        }
                    } catch(e) {
                        console.error('Error in clipboard script:', e);
                    }
                })();
            `;
            document.head.appendChild(clipboardScript);
            clipboardScript.remove();

            // Wait for the script to execute
            await new Promise(resolve => setTimeout(resolve, 300));
        } catch (e) {
            console.error('Clipboard approach failed:', e);
        }

        // Check if the clipboard approach worked
        if (amountInput.value === amount.toString()) {
            console.log('Successfully set amount via clipboard paste');
            return true;
        }

        // If clipboard approach failed, try the direct approach with more aggressive clearing
        console.log('Clipboard approach failed, trying direct approach with aggressive clearing');

        // AGGRESSIVE CLEARING: Try multiple approaches in sequence

        // 1. Clear via direct value setting
        amountInput.value = '';
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 2. Select all and delete
        amountInput.select();
        document.execCommand('delete');

        // 3. Simulate backspace key presses to clear any remaining text
        const currentValue = amountInput.value;
        if (currentValue) {
            for (let i = 0; i < 20; i++) { // Press backspace multiple times to ensure clearing
                const backspaceEvent = new KeyboardEvent('keydown', {
                    key: 'Backspace',
                    code: 'Backspace',
                    keyCode: 8,
                    which: 8,
                    bubbles: true
                });
                amountInput.dispatchEvent(backspaceEvent);

                // Try to clear the value directly as well
                if (i % 2 === 0) {
                    amountInput.value = '';
                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
            }
        }

        await new Promise(resolve => setTimeout(resolve, 200));

        // Now set the new value using direct assignment
        amountInput.value = amount.toString();
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
        amountInput.dispatchEvent(new Event('change', { bubbles: true }));

        // Also try using execCommand to insert the text
        amountInput.select();
        document.execCommand('insertText', false, amount.toString());
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
        amountInput.dispatchEvent(new Event('change', { bubbles: true }));

        // If the value is still not set correctly, try one more approach with a script
        if (amountInput.value !== amount.toString()) {
            console.log('Direct approaches failed, trying page script approach');

            // Create a script to set the amount in the page context
            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    try {
                        // Find all potential amount inputs
                        const amountInputs = document.querySelectorAll('.block--bet-amount input, .value__val input, input[type="text"], input[type="number"]');
                        console.log('Found ' + amountInputs.length + ' potential amount inputs');

                        // Try to set the amount on each input
                        for (let i = 0; i < amountInputs.length; i++) {
                            const input = amountInputs[i];

                            // AGGRESSIVE APPROACH: Create a new input element and replace the old one
                            try {
                                const parent = input.parentElement;
                                if (parent) {
                                    // Create a new input with the same properties
                                    const newInput = document.createElement('input');
                                    newInput.type = input.type || 'text';
                                    newInput.className = input.className;
                                    newInput.id = input.id;
                                    newInput.name = input.name;
                                    newInput.placeholder = input.placeholder;

                                    // Set the value directly
                                    newInput.value = '${amount}';

                                    // Replace the old input
                                    parent.replaceChild(newInput, input);

                                    // Dispatch events on the new input
                                    newInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    newInput.dispatchEvent(new Event('change', { bubbles: true }));

                                    console.log('Replaced input with new one containing value: ${amount}');
                                }
                            } catch (replaceError) {
                                console.error('Error replacing input:', replaceError);

                                // Fallback: Try to set the value directly
                                try {
                                    // Focus and clear
                                    input.focus();

                                    // Clear the input
                                    input.value = '';
                                    input.dispatchEvent(new Event('input', { bubbles: true }));

                                    // Select all and delete
                                    input.select();
                                    document.execCommand('delete');

                                    // Set new value
                                    input.value = '${amount}';
                                    input.dispatchEvent(new Event('input', { bubbles: true }));
                                    input.dispatchEvent(new Event('change', { bubbles: true }));

                                    console.log('Set value directly on input ' + i + ': ' + input.value);
                                } catch (e) {
                                    console.error('Error setting value on input ' + i + ':', e);
                                }
                            }
                        }
                    } catch(e) {
                        console.error('Error in amount setting script:', e);
                    }
                })();
            `;
            document.head.appendChild(script);
            script.remove();

            // Wait for the script to execute
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // Final verification and logging
        const finalValue = amountInput.value;
        if (finalValue === amount.toString()) {
            console.log('Successfully set amount to:', amount);
            return true;
        } else {
            console.warn('Could not verify amount was set correctly. Current value:', finalValue, 'Expected:', amount.toString());
            // Continue anyway, as the value might have been set correctly despite the verification failing
            return false;
        }
    } catch (error) {
        console.error('Error setting amount:', error);
        throw error;
    }
}

// Select expiry time
async function selectExpiry(expirySelector, expirySeconds) {
    try {
        // If it's a dropdown, try to select the option
        if (expirySelector.tagName === 'SELECT') {
            // Find the closest option
            let bestOption = null;
            let bestDiff = Infinity;

            for (const option of expirySelector.options) {
                const value = parseInt(option.value);
                if (!isNaN(value)) {
                    const diff = Math.abs(value - expirySeconds);
                    if (diff < bestDiff) {
                        bestDiff = diff;
                        bestOption = option;
                    }
                }
            }

            if (bestOption) {
                expirySelector.value = bestOption.value;
                expirySelector.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`Selected expiry: ${bestOption.textContent}`);
                return;
            }
        }

        // If it's a button or div that opens a dropdown
        await clickElement(expirySelector);

        // Wait a moment for any dropdown to appear
        await new Promise(resolve => setTimeout(resolve, 500));

        // Look for expiry options in the dropdown
        const expiryOptions = document.querySelectorAll('.expiry-option, [data-expiry], li, button, div');
        let bestMatch = null;
        let bestDiff = Infinity;

        for (const option of expiryOptions) {
            const text = option.textContent || '';
            const expiryValue = parseExpiryTime(text);

            if (expiryValue) {
                const diff = Math.abs(expiryValue - expirySeconds);
                if (diff < bestDiff) {
                    bestDiff = diff;
                    bestMatch = option;
                }
            }
        }

        if (bestMatch) {
            highlightElement(bestMatch);
            await clickElement(bestMatch);
            console.log(`Selected expiry option: ${bestMatch.textContent}`);
        } else {
            console.warn(`Could not find suitable expiry option for ${expirySeconds} seconds, using default`);
        }
    } catch (error) {
        console.error('Error selecting expiry:', error);
        console.warn('Continuing with default expiry');
    }
}

// Parse expiry time text to seconds
function parseExpiryTime(expiryText) {
    const minutes = expiryText.match(/(\d+)\s*m/i);
    const seconds = expiryText.match(/(\d+)\s*s/i);

    if (minutes) {
        return parseInt(minutes[1]) * 60;
    } else if (seconds) {
        return parseInt(seconds[1]);
    }

    return null;
}

// Helper function to find an element using multiple possible selectors
function findElement(selector) {
    // If selector is an array, try each one until we find an element
    if (Array.isArray(selector)) {
        for (const s of selector) {
            const element = document.querySelector(s);
            if (element) {
                console.log(`Found element with selector: ${s}`);
                return element;
            }
        }
        return null;
    }

    // If selector is a string, use querySelector directly
    return document.querySelector(selector);
}

// Helper function to wait for an element to appear
function waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const element = findElement(selector);

        if (element) {
            resolve(element);
            return;
        }

        const startTime = Date.now();

        const interval = setInterval(() => {
            const element = findElement(selector);

            if (element) {
                clearInterval(interval);
                resolve(element);
            } else if (Date.now() - startTime > timeout) {
                clearInterval(interval);
                reject(new Error(`Timeout waiting for element: ${selector}`));
            }
        }, 100);
    });
}

// Helper function to highlight an element (silent mode - only logs to console)
function highlightElement(element) {
    if (!element) {
        console.warn('Cannot highlight null element');
        return;
    }

    // Only log to console, no visual highlighting
    console.log('Element interaction:', {
        element: element,
        tagName: element.tagName,
        id: element.id,
        className: element.className,
        type: element.type
    });

    // Scroll the element into view if needed, but without visual effects
    const rect = element.getBoundingClientRect();
    const isVisible = (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );

    if (!isVisible) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Simulate typing in an input field
async function simulateTyping(input, text) {
    console.log('Simulating typing of text:', text, 'in input:', input);

    // Make sure the input is focused
    input.focus();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Clear the input first - try multiple approaches
    // 1. Direct value setting
    input.value = '';
    input.dispatchEvent(new Event('input', { bubbles: true }));

    // 2. Select all and delete
    input.select();
    try {
        document.execCommand('delete');
    } catch (e) {
        console.log('execCommand delete failed:', e);
    }

    await new Promise(resolve => setTimeout(resolve, 200));

    // Type each character with a small delay
    for (let i = 0; i < text.length; i++) {
        const char = text[i];

        // Determine the correct key code and code value
        let keyCode, codeValue;
        if (/^\d$/.test(char)) {
            // For digits
            keyCode = char.charCodeAt(0);
            codeValue = `Digit${char}`;
        } else if (/^[a-zA-Z]$/.test(char)) {
            // For letters
            keyCode = char.toLowerCase().charCodeAt(0);
            codeValue = `Key${char.toUpperCase()}`;
        } else if (char === '.') {
            // For decimal point
            keyCode = 190;
            codeValue = 'Period';
        } else {
            // For other characters
            keyCode = char.charCodeAt(0);
            codeValue = `Key${char.toUpperCase()}`;
        }

        // Create and dispatch keydown event
        const keydownEvent = new KeyboardEvent('keydown', {
            key: char,
            code: codeValue,
            keyCode: keyCode,
            which: keyCode,
            bubbles: true,
            cancelable: true
        });
        input.dispatchEvent(keydownEvent);

        // Update the value
        const currentValue = input.value;
        input.value = currentValue + char;

        // Dispatch input event
        input.dispatchEvent(new Event('input', { bubbles: true }));

        // Create and dispatch keyup event
        const keyupEvent = new KeyboardEvent('keyup', {
            key: char,
            code: codeValue,
            keyCode: keyCode,
            which: keyCode,
            bubbles: true,
            cancelable: true
        });
        input.dispatchEvent(keyupEvent);

        // Small delay between keypresses
        await new Promise(resolve => setTimeout(resolve, 50));
    }

    // Dispatch change event after all typing is done
    input.dispatchEvent(new Event('change', { bubbles: true }));

    // Log the final value
    console.log('After simulated typing, input value is:', input.value);

    // If the value wasn't set correctly, try one more approach
    if (input.value !== text) {
        console.log('Simulated typing failed to set correct value, trying alternative approach');

        // Try using execCommand
        input.select();
        try {
            document.execCommand('insertText', false, text);
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('After execCommand, input value is:', input.value);
        } catch (e) {
            console.log('execCommand insertText failed:', e);

            // Last resort: direct value setting
            input.value = text;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('After direct value setting, input value is:', input.value);
        }
    }
}

// Helper function to clear and set an input value
async function clearAndSetInputValue(input, value) {
    if (!input) {
        console.error('Input element is null');
        return false;
    }

    console.log('Clearing and setting input value to:', value);

    // Make sure the input is visible and focused
    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 300));

    // Focus the input
    input.focus();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Try multiple approaches to clear the input

    // Approach 1: Direct value setting
    input.value = '';
    input.dispatchEvent(new Event('input', { bubbles: true }));

    // Approach 2: Select all and delete
    input.select();
    try {
        // Note: document.execCommand is deprecated but still works in most browsers
        document.execCommand('delete');
    } catch (e) {
        console.log('execCommand delete failed:', e);
    }

    // Approach 3: Simulate backspace key presses
    const currentValue = input.value;
    if (currentValue) {
        for (let i = 0; i < currentValue.length; i++) {
            // Dispatch backspace key event
            const backspaceEvent = new KeyboardEvent('keydown', {
                key: 'Backspace',
                code: 'Backspace',
                keyCode: 8,
                which: 8,
                bubbles: true,
                cancelable: true
            });
            input.dispatchEvent(backspaceEvent);

            // Update value
            input.value = input.value.substring(0, input.value.length - 1);

            // Dispatch input event
            input.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    await new Promise(resolve => setTimeout(resolve, 100));

    // Now set the new value using multiple approaches

    // Approach 1: Direct value setting
    input.value = value;
    input.dispatchEvent(new Event('input', { bubbles: true }));
    input.dispatchEvent(new Event('change', { bubbles: true }));

    // Approach 2: Using setAttribute
    input.setAttribute('value', value);

    // Approach 3: Using execCommand
    try {
        input.select();
        // Note: document.execCommand is deprecated but still works in most browsers
        document.execCommand('insertText', false, value);
    } catch (e) {
        console.log('execCommand insertText failed:', e);
    }

    // Verify the value was set correctly
    if (input.value !== value) {
        console.warn('Failed to set input value correctly. Current:', input.value, 'Expected:', value);

        // One last attempt - try to use a script in the page context
        const script = document.createElement('script');
        script.textContent = `
            (function() {
                try {
                    const input = document.querySelector('.block--bet-amount input, .value__val input');
                    if (input) {
                        input.value = '${value}';
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                } catch(e) {
                    console.error('Error in value setting script:', e);
                }
            })();
        `;
        document.head.appendChild(script);
        script.remove();

        // Wait a moment for the script to execute
        await new Promise(resolve => setTimeout(resolve, 300));

        // Check if the value was set now
        return input.value === value;
    }

    return true;
}

// Helper function to click an element
async function clickElement(selectorOrElement) {
    const element = typeof selectorOrElement === 'string'
        ? await waitForElement(selectorOrElement)
        : selectorOrElement;

    if (!element) {
        throw new Error(`Element not found: ${selectorOrElement}`);
    }

    try {
        // Try multiple methods to click the element

        // Method 1: Standard click
        element.click();

        // Method 2: MouseEvent
        if (document.activeElement !== element) {
            const clickEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(clickEvent);
        }

        // Method 3: Focus and Enter key
        if (document.activeElement !== element) {
            element.focus();
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
            });
            element.dispatchEvent(enterEvent);
        }

        console.log('Clicked element:', element);
    } catch (error) {
        console.error('Error clicking element:', error);
        throw error;
    }
}

// Force create the floating interface - this is a direct approach that should always work
function forceCreateFloatingInterface() {
    console.log('Force creating floating interface...');

    // Remove any existing floating window
    if (floatingWindow) {
        try {
            document.body.removeChild(floatingWindow);
        } catch (e) {
            console.error('Error removing existing floating window:', e);
        }
        floatingWindow = null;
        floatingFrame = null;
    }

    try {
        // Create container for the floating window
        floatingWindow = document.createElement('div');
        floatingWindow.id = 'botFloatingWindow';

        // Try to load saved position
        let initialTop = '70px';
        let initialLeft = '10px';

        try {
            const savedPosition = localStorage.getItem('botPosition');
            if (savedPosition) {
                const position = JSON.parse(savedPosition);
                initialLeft = position.left;
                initialTop = position.top;
                console.log('Loaded saved position:', initialLeft, initialTop);
            }
        } catch (e) {
            console.error('Failed to load position:', e);
        }

        // Apply styles directly to ensure they take effect and match the bot's appearance exactly
        Object.assign(floatingWindow.style, {
            position: 'fixed',
            zIndex: '9999999',
            top: initialTop,
            left: initialLeft,
            width: '450px', // Further increased width to make the Trading Bot interface wider
            height: '600px', // Increased height to accommodate Neural Pulse content
            borderRadius: '10px',
            overflow: 'hidden', // Hide overflow
            backgroundColor: 'transparent', // Make container transparent
            border: 'none', // No border on container
            padding: '0',
            margin: '0'
        });

        // Create iframe to load the floating interface
        floatingFrame = document.createElement('iframe');

        // Apply styles directly to the iframe to match the bot's appearance exactly
        Object.assign(floatingFrame.style, {
            border: 'none',
            width: '100%', // Fill the container exactly
            height: '100%', // Fill the container exactly
            backgroundColor: 'transparent',
            borderRadius: '10px',
            boxShadow: '0 5px 20px rgba(0, 0, 0, 0.5)',
            position: 'absolute',
            top: '0',
            left: '0',
            padding: '0',
            margin: '0'
        });

        // Get the extension URL for the floating interface
        const extensionUrl = chrome.runtime.getURL('floating.html');
        console.log('Loading floating interface from:', extensionUrl);

        // Add load event listener to ensure iframe loads properly
        floatingFrame.onload = function() {
            console.log('Floating interface iframe loaded successfully');
            showNotification('Trading Bot activated', 'success');

            // Send a message to the iframe to initialize it
            setTimeout(() => {
                try {
                    // Send initialization message
                    floatingFrame.contentWindow.postMessage({
                        action: 'initialize',
                        settings: settings
                    }, '*');
                    console.log('Initialization message sent to floating interface');

                    // Also send current balance
                    if (currentBalance > 0) {
                        floatingFrame.contentWindow.postMessage({
                            action: 'updateBalance',
                            balance: currentBalance,
                            realBalance: true
                        }, '*');
                        console.log('Sent balance to floating interface:', currentBalance);
                    }
                } catch (error) {
                    console.error('Error sending initialization message:', error);
                }
            }, 500);
        };

        // Set the source after adding the event listener
        floatingFrame.src = extensionUrl;

        // Add iframe to container
        floatingWindow.appendChild(floatingFrame);

        // Add container to page
        document.body.appendChild(floatingWindow);

        // Make the floating window draggable
        makeElementDraggable(floatingWindow);

        console.log('Floating interface created and added to page');
        return true;
    } catch (error) {
        console.error('Error creating floating interface:', error);
        showNotification('Error creating floating interface: ' + error.message, 'error');
        return false;
    }
}

// Make an element draggable
function makeElementDraggable(element) {
    let isDragging = false;
    let offsetX, offsetY;
    let dragMode = 'header'; // 'header' or 'anywhere'

    // Create a draggable header that exactly matches the bot's header
    const header = document.createElement('div');
    header.style.position = 'absolute';
    header.style.top = '0';
    header.style.left = '0';
    header.style.width = '100%'; // Full width of container
    header.style.height = '40px'; // Match the bot's header height
    header.style.cursor = 'grab';
    header.style.zIndex = '10000000';
    header.style.backgroundColor = 'transparent';
    header.style.borderTopLeftRadius = '10px';
    header.style.borderTopRightRadius = '10px';
    header.style.padding = '0';
    header.style.margin = '0';

    element.appendChild(header);

    // Create a full-size overlay for double-click dragging
    const overlay = document.createElement('div');
    overlay.style.position = 'absolute';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.zIndex = '9999999'; // Below the header but above everything else
    overlay.style.backgroundColor = 'transparent';
    overlay.style.display = 'none'; // Hidden by default
    overlay.style.cursor = 'move';

    element.appendChild(overlay);

    // Function to start dragging
    function startDrag(e, fromElement) {
        isDragging = true;
        offsetX = e.clientX - element.getBoundingClientRect().left;
        offsetY = e.clientY - element.getBoundingClientRect().top;
        element.style.transition = 'none';
        fromElement.style.cursor = fromElement === header ? 'grabbing' : 'move';

        // Show overlay when dragging from anywhere
        if (dragMode === 'anywhere') {
            overlay.style.display = 'block';
        }
    }

    // Function to end dragging
    function endDrag() {
        if (!isDragging) return;

        isDragging = false;
        element.style.transition = 'opacity 0.3s ease';
        header.style.cursor = 'grab';
        overlay.style.display = 'none';

        // Save position to localStorage for persistence
        try {
            const position = {
                left: element.style.left,
                top: element.style.top
            };
            localStorage.setItem('botPosition', JSON.stringify(position));
        } catch (e) {
            console.error('Failed to save position:', e);
        }
    }

    // Header drag events
    header.addEventListener('mousedown', function(e) {
        startDrag(e, header);
    });

    // Double-click anywhere to enable dragging
    element.addEventListener('dblclick', function(e) {
        // Don't activate if clicking on a button or input
        if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' ||
            e.target.tagName === 'SELECT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        dragMode = 'anywhere';
        overlay.style.display = 'block';
        startDrag(e, overlay);
    });

    // Mouse move for dragging
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        const x = e.clientX - offsetX;
        const y = e.clientY - offsetY;

        element.style.left = x + 'px';
        element.style.top = y + 'px';
    });

    // Mouse up to end dragging
    document.addEventListener('mouseup', function() {
        endDrag();
    });

    // Touch events for mobile
    header.addEventListener('touchstart', function(e) {
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        startDrag(mouseEvent, header);
    });

    // Double-tap for mobile
    let lastTap = 0;
    element.addEventListener('touchstart', function(e) {
        const now = new Date().getTime();
        const timeDiff = now - lastTap;

        if (timeDiff < 300 && timeDiff > 0) {
            // Double tap detected
            dragMode = 'anywhere';
            overlay.style.display = 'block';

            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            startDrag(mouseEvent, overlay);
        }

        lastTap = now;
    });

    document.addEventListener('touchmove', function(e) {
        if (!isDragging) return;

        e.preventDefault(); // Prevent scrolling while dragging
        const touch = e.touches[0];
        const x = touch.clientX - offsetX;
        const y = touch.clientY - offsetY;

        element.style.left = x + 'px';
        element.style.top = y + 'px';
    });

    document.addEventListener('touchend', function() {
        endDrag();
    });

    // Click outside the element to exit "anywhere" drag mode
    document.addEventListener('click', function(e) {
        if (!element.contains(e.target) && dragMode === 'anywhere') {
            dragMode = 'header';
            overlay.style.display = 'none';
        }
    });
}

// Create and inject the floating interface
function createFloatingInterface() {
    // Check if floating window already exists
    if (floatingWindow) {
        // If it exists but is not visible, make it visible
        if (floatingWindow.style.display === 'none') {
            floatingWindow.style.display = 'block';
            showNotification('Trading Bot activated', 'success');
        }
        return true;
    }

    // Use the force create function to ensure a consistent approach
    const result = forceCreateFloatingInterface();

    // Send current balance to the floating interface after a short delay
    if (result && floatingFrame) {
        setTimeout(() => {
            try {
                if (currentBalance > 0) {
                    floatingFrame.contentWindow.postMessage({
                        action: 'updateBalance',
                        balance: currentBalance,
                        realBalance: true
                    }, '*');
                    console.log('Sent initial balance to floating interface:', currentBalance);
                }
            } catch (error) {
                console.error('Error sending initial balance to floating interface:', error);
            }
        }, 1500);
    }

    return result;
}

// Remove the floating interface
function removeFloatingInterface() {
    if (floatingWindow) {
        document.body.removeChild(floatingWindow);
        floatingWindow = null;
        floatingFrame = null;
        console.log('Floating interface removed');
    }
}

// Show notification in the page (visual popup disabled, functionality preserved)
function showNotification(message, type = 'info') {
    console.log(`Notification (${type}): ${message}`);

    // Keep functionality but don't show visual popup
    // Just log the notification for debugging purposes
    // The functionality is preserved but no visual popup is displayed
}

// Show a congratulations popup when profit target is reached
function showCongratulationsPopup(profit) {
    console.log('Profit target reached! Total profit: $' + profit.toFixed(2));

    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '10000001';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';

    // Create popup
    const popup = document.createElement('div');
    popup.style.backgroundColor = '#1e293b';
    popup.style.borderRadius = '10px';
    popup.style.padding = '30px';
    popup.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.5)';
    popup.style.textAlign = 'center';
    popup.style.maxWidth = '400px';
    popup.style.width = '80%';
    popup.style.border = '2px solid #10b981';
    popup.style.color = 'white';
    popup.style.fontFamily = 'Arial, sans-serif';

    // Add confetti animation
    const confetti = document.createElement('div');
    confetti.innerHTML = `
        <div style="position: absolute; top: -20px; left: 0; width: 100%; height: 20px; overflow: hidden;">
            ${Array(20).fill().map((_, i) =>
                `<div style="
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    background-color: ${['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'][i % 5]};
                    top: -10px;
                    left: ${Math.random() * 100}%;
                    animation: confetti-fall ${1 + Math.random() * 3}s ease-in-out ${Math.random() * 2}s infinite;
                    transform: rotate(${Math.random() * 360}deg);
                "></div>`
            ).join('')}
        </div>
    `;

    // Add animation style
    const style = document.createElement('style');
    style.textContent = `
        @keyframes confetti-fall {
            0% { transform: translateY(0) rotate(0deg); opacity: 1; }
            100% { transform: translateY(1000px) rotate(720deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Add content
    const title = document.createElement('h2');
    title.textContent = '🎉 Congratulations! 🎉';
    title.style.color = '#10b981';
    title.style.fontSize = '24px';
    title.style.marginBottom = '15px';

    const message = document.createElement('p');
    message.innerHTML = `You've reached your profit target!<br>Total profit: <strong>$${profit.toFixed(2)}</strong>`;
    message.style.fontSize = '16px';
    message.style.lineHeight = '1.5';
    message.style.marginBottom = '20px';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.padding = '10px 20px';
    closeButton.style.backgroundColor = '#10b981';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '14px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.transition = 'background-color 0.2s';

    closeButton.addEventListener('mouseover', () => {
        closeButton.style.backgroundColor = '#0d9668';
    });

    closeButton.addEventListener('mouseout', () => {
        closeButton.style.backgroundColor = '#10b981';
    });

    closeButton.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    // Assemble popup
    popup.appendChild(confetti);
    popup.appendChild(title);
    popup.appendChild(message);
    popup.appendChild(closeButton);
    overlay.appendChild(popup);

    // Add to document
    document.body.appendChild(overlay);

    // Auto close after 10 seconds
    setTimeout(() => {
        if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    }, 10000);
}

// Force complete replacement of amount field - most aggressive approach
function forceCompleteAmountReplacement(amount) {
    return new Promise((resolve) => {
        try {
            console.log('FORCE COMPLETE REPLACEMENT: Using most aggressive approach to set amount:', amount);
            showNotification(`Using emergency approach to set amount to $${amount}`, 'warning');

            // Create a message handler to listen for the response
            const messageHandler = function(event) {
                // Only accept messages from the same window
                if (event.source !== window) return;

                const message = event.data;
                if (message && message.from === 'pocketOptionBot' && message.action === 'amountSet') {
                    console.log('Received amount set response:', message);
                    window.removeEventListener('message', messageHandler);
                    resolve(message.success);
                }
            };

            // Add the message handler
            window.addEventListener('message', messageHandler);

            // Send a message to the page script to use the specialized function
            window.postMessage({
                action: 'forceCompleteAmountReplacement',
                amount: amount
            }, '*');

            // Set a timeout to resolve the promise if no response is received
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                console.log('No response from page script, assuming emergency amount set failed');
                resolve(false);
            }, 5000);

        } catch (e) {
            console.error('Error in force complete amount replacement:', e);
            resolve(false);
        }
    });
}

// Human-like approach to setting amount - select all and paste in one operation
function humanLikeAmountReplacement(amount) {
    return new Promise((resolve) => {
        try {
            console.log('HUMAN-LIKE REPLACEMENT: Using approach that mimics human behavior to set amount:', amount);
            showNotification(`Using human-like approach to set amount to $${amount}`, 'info');

            // Create a message handler to listen for the response
            const messageHandler = function(event) {
                // Only accept messages from the same window
                if (event.source !== window) return;

                const message = event.data;
                if (message && message.from === 'pocketOptionBot' && message.action === 'amountSet') {
                    console.log('Received amount set response:', message);
                    window.removeEventListener('message', messageHandler);
                    resolve(message.success);
                }
            };

            // Add the message handler
            window.addEventListener('message', messageHandler);

            // Send a message to the page script to use the human-like approach
            window.postMessage({
                action: 'humanLikeAmountReplacement',
                amount: amount
            }, '*');

            // Set a timeout to resolve the promise if no response is received
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                console.log('No response from page script, assuming human-like amount set failed');
                resolve(false);
            }, 5000);

        } catch (e) {
            console.error('Error in human-like amount replacement:', e);
            resolve(false);
        }
    });
}

// Set amount via the page script without executing a trade
function setAmountViaPageScript(amount) {
    return new Promise((resolve) => {
        try {
            console.log('Setting amount via page script:', amount);

            // Create a message handler to listen for the response
            const messageHandler = function(event) {
                // Only accept messages from the same frame
                if (event.source !== window) return;

                const message = event.data;

                // Check if this is a response from our page script
                if (message && message.from === 'pocketOptionBot' && message.action === 'amountSet') {
                    console.log('Amount set result from page script:', message);

                    // Check if the amount was verified correctly
                    if (message.success && message.verified) {
                        console.log('Amount set and verified successfully');
                        window.removeEventListener('message', messageHandler);
                        resolve(true);
                    } else if (message.success && !message.verified) {
                        console.warn('Amount set but verification failed. Expected:', amount, 'Actual:', message.actualAmount);

                        // Try the emergency approach
                        console.log('Trying emergency approach to force complete replacement');
                        window.removeEventListener('message', messageHandler);

                        // Use the specialized function for completely replacing amounts
                        forceCompleteAmountReplacement(amount).then(success => {
                            resolve(success);
                        });
                    } else {
                        console.error('Failed to set amount:', message.error);
                        window.removeEventListener('message', messageHandler);
                        resolve(false);
                    }
                }
            };

            // Add the message handler
            window.addEventListener('message', messageHandler);

            // Send a message to the page script to set the amount
            window.postMessage({
                action: 'setAmount',
                amount: amount
            }, '*');

            // Set a timeout to resolve the promise if no response is received
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                console.log('No response from page script, assuming amount set failed');
                resolve(false);
            }, 5000);

        } catch (e) {
            console.error('Error setting amount via page script:', e);
            resolve(false);
        }
    });
}

// Find the amount input field via the page script
function findAmountInputViaPageScript() {
    return new Promise((resolve) => {
        try {
            console.log('Finding amount input via page script');

            // Create a message handler to listen for the response
            const messageHandler = function(event) {
                // Only accept messages from the same frame
                if (event.source !== window) return;

                const message = event.data;

                // Check if this is a response from our page script
                if (message && message.from === 'pocketOptionBot' && message.action === 'amountInputFound') {
                    console.log('Amount input found result from page script:', message);

                    // Remove the event listener
                    window.removeEventListener('message', messageHandler);

                    // Resolve the promise with the result
                    resolve({
                        success: message.success,
                        details: message.details
                    });
                }
            };

            // Add the message handler
            window.addEventListener('message', messageHandler);

            // Send a message to the page script to find the amount input
            window.postMessage({
                action: 'findAmountInput'
            }, '*');

            // Set a timeout to resolve the promise if no response is received
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                console.log('No response from page script, assuming amount input not found');
                resolve({ success: false });
            }, 5000);

        } catch (e) {
            console.error('Error finding amount input via page script:', e);
            resolve({ success: false, error: e.message });
        }
    });
}

// Inject a custom script directly into the page context
function injectCustomScript(scriptContent) {
    return new Promise((resolve) => {
        try {
            console.log('Injecting custom script');

            // Create a message handler to listen for any response
            const messageHandler = function(event) {
                // Only accept messages from the same frame
                if (event.source !== window) return;

                const message = event.data;

                // Check if this is a response from our page script
                if (message && message.from === 'pocketOptionBot') {
                    console.log('Response from injected script:', message);

                    // We'll keep listening for messages, but resolve the promise
                    // to indicate the script was injected successfully
                    resolve(true);
                }
            };

            // Add the message handler
            window.addEventListener('message', messageHandler);

            // Send a message to the page script to inject the custom script
            window.postMessage({
                action: 'injectScript',
                scriptContent: scriptContent
            }, '*');

            // Set a timeout to resolve the promise after a short delay
            // We don't remove the event listener since we want to keep receiving messages
            setTimeout(() => {
                console.log('Script injection completed');
                resolve(true);
            }, 1000);

        } catch (e) {
            console.error('Error injecting custom script:', e);
            resolve(false);
        }
    });
}

// Set amount using direct DOM manipulation and script injection
async function setAmountWithRobustApproach(amount) {
    console.log('Setting amount with robust approach:', amount);

    // First check for the amount modal UI
    const modalScript = `
        (function() {
            // Check if the amount modal is present
            const modal = document.querySelector('.drop-down-modal.trading-panel-modal.amount-list-modal');
            if (modal) {
                console.log('Found amount modal in the DOM');

                // Check if it's visible
                const style = window.getComputedStyle(modal);
                if (style.display !== 'none' && style.visibility !== 'hidden') {
                    console.log('Amount modal is visible');

                    // First try to clear any existing value in the amount field
                    const amountField = modal.querySelector('.amount-field');
                    if (amountField) {
                        console.log('Current amount field value:', amountField.textContent);

                        // Try multiple clearing approaches
                        const clearingApproaches = [
                            // Approach 1: Set to empty or just the currency symbol
                            () => {
                                amountField.textContent = '$';
                                amountField.dispatchEvent(new Event('input', { bubbles: true }));
                                amountField.dispatchEvent(new Event('change', { bubbles: true }));
                            },
                            // Approach 2: Use innerHTML
                            () => {
                                amountField.innerHTML = '$';
                                amountField.dispatchEvent(new Event('input', { bubbles: true }));
                                amountField.dispatchEvent(new Event('change', { bubbles: true }));
                            },
                            // Approach 3: Try to replace the entire content
                            () => {
                                const parent = amountField.parentElement;
                                if (parent) {
                                    const originalHTML = parent.innerHTML;
                                    const newHTML = originalHTML.replace(/>([^<]*\\d[^<]*)</g, '>$<');
                                    parent.innerHTML = newHTML;
                                }
                            },
                            // Approach 4: Try to use DOM manipulation to recreate the element
                            () => {
                                const parent = amountField.parentElement;
                                if (parent) {
                                    const newField = document.createElement('div');
                                    newField.className = amountField.className;
                                    newField.textContent = '$';
                                    parent.replaceChild(newField, amountField);
                                }
                            }
                        ];

                        // Try each approach
                        for (let i = 0; i < clearingApproaches.length; i++) {
                            try {
                                clearingApproaches[i]();
                                console.log('Applied clearing approach ' + (i+1));

                                // Check if the field is cleared after each approach
                                setTimeout(() => {
                                    try {
                                        const currentField = modal.querySelector('.amount-field');
                                        if (currentField) {
                                            const currentValue = currentField.textContent.replace(/[^0-9.]/g, '');
                                            console.log('After approach ' + (i+1) + ', amount field value:', currentValue);
                                        }
                                    } catch (e) {
                                        // Ignore errors in checking
                                    }
                                }, 50 * (i + 1));
                            } catch (e) {
                                console.error('Error with clearing approach ' + (i+1) + ':', e);
                            }
                        }
                    }

                    // Try to use preset options
                    const presetOptions = modal.querySelectorAll('.end-block .item');
                    console.log('Found ' + presetOptions.length + ' preset options');

                    // Look for a preset that matches our amount
                    let presetFound = false;
                    for (const option of presetOptions) {
                        const optionValue = option.textContent.replace(/[^0-9.]/g, '');
                        if (optionValue === '${amount}' || optionValue === ${parseFloat(amount)}) {
                            console.log('Found matching preset option: ' + option.textContent);
                            option.click();
                            presetFound = true;
                            break;
                        }
                    }

                    if (!presetFound) {
                        // Try using the virtual keyboard
                        const virtualKeyboard = modal.querySelector('.virtual-keyboard');
                        if (virtualKeyboard) {
                            console.log('Using virtual keyboard');

                            // First clear any existing value by clicking backspace
                            const backspaceButton = virtualKeyboard.querySelector('.virtual-keyboard__input svg[data-src*="back.svg"]');
                            if (backspaceButton) {
                                const backspaceParent = backspaceButton.closest('.virtual-keyboard__input');
                                if (backspaceParent) {
                                    // Click backspace many more times to ensure the field is completely cleared
                                    for (let i = 0; i < 20; i++) {
                                        backspaceParent.click();
                                        // Add a small delay between clicks
                                        if (i % 5 === 0) {
                                            // Use setTimeout to create a small delay
                                            setTimeout(() => {}, 10);
                                        }
                                    }

                                    // Check if the field is cleared
                                    if (amountField) {
                                        const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                                        console.log('Amount field value after backspace clearing:', currentValue);

                                        // If not cleared, try direct approach
                                        if (currentValue && currentValue !== '') {
                                            console.log('Field not cleared by backspace, trying direct approach');
                                            amountField.textContent = '$';
                                            amountField.dispatchEvent(new Event('input', { bubbles: true }));
                                            amountField.dispatchEvent(new Event('change', { bubbles: true }));
                                        }
                                    }
                                }
                            }

                            // Input each digit with small delays between
                            const amountStr = '${amount}';
                            for (let i = 0; i < amountStr.length; i++) {
                                const char = amountStr[i];
                                const digitButtons = virtualKeyboard.querySelectorAll('.virtual-keyboard__input');

                                setTimeout(() => {
                                    for (const button of digitButtons) {
                                        if (button.textContent.trim() === char) {
                                            console.log('Clicking button for: ' + char);
                                            button.click();
                                            break;
                                        }
                                    }
                                }, i * 50); // Add increasing delay for each digit
                            }
                        }

                        // Also try to directly modify the amount field after a short delay
                        setTimeout(() => {
                            if (amountField) {
                                console.log('Directly modifying amount field after delay');

                                // Check current value
                                const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                                console.log('Current amount field value before direct modification:', currentValue);

                                // If it's not what we want, set it directly
                                if (currentValue !== '${amount}') {
                                    amountField.textContent = '$${amount}';
                                    amountField.dispatchEvent(new Event('input', { bubbles: true }));
                                    amountField.dispatchEvent(new Event('change', { bubbles: true }));

                                    // Also try innerHTML
                                    amountField.innerHTML = '$${amount}';

                                    console.log('Set amount field directly to: $${amount}');
                                }
                            }

                            // Reset the multiplier to 1
                            const multiplyField = modal.querySelector('.multiply-field');
                            if (multiplyField) {
                                multiplyField.value = '1';
                                multiplyField.dispatchEvent(new Event('input', { bubbles: true }));
                                multiplyField.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        }, 300); // Wait a bit to ensure keyboard input has finished
                    }

                    return true;
                } else {
                    console.log('Amount modal exists but is not visible');

                    // Try to find and click the trigger to open the modal
                    const potentialTriggers = document.querySelectorAll('.amount-field, [data-amount], .bet-amount, .trade-amount, .block--bet-amount');
                    for (const trigger of potentialTriggers) {
                        console.log('Clicking potential trigger: ' + trigger.textContent);
                        trigger.click();

                        // Try clicking any child elements that might be the actual clickable part
                        const childElements = trigger.querySelectorAll('*');
                        for (const child of childElements) {
                            try {
                                child.click();
                            } catch (e) {
                                // Ignore errors on child clicks
                            }
                        }

                        break;
                    }
                }
            } else {
                console.log('Amount modal not found in the DOM');

                // Try to find and click elements that might open the modal
                const potentialTriggers = document.querySelectorAll('.amount-field, [data-amount], .bet-amount, .trade-amount, .block--bet-amount, .value__val');
                for (const trigger of potentialTriggers) {
                    console.log('Clicking potential modal trigger: ' + trigger.textContent);
                    trigger.click();
                    break;
                }
            }
        })();
    `;

    // Inject the modal handling script
    console.log('Injecting modal handling script');
    await injectCustomScript(modalScript);

    // Wait a moment for any modal to appear and be handled
    await new Promise(resolve => setTimeout(resolve, 1000));

    // First try the human-like approach
    console.log('Starting with the human-like approach to set amount');
    const humanLikeResult = await humanLikeAmountReplacement(amount);
    if (humanLikeResult) {
        console.log('Successfully set amount using human-like replacement');
        return true;
    }

    // If that fails, try the most aggressive approach
    console.log('Human-like approach failed, trying the most aggressive approach');
    const forceResult = await forceCompleteAmountReplacement(amount);
    if (forceResult) {
        console.log('Successfully set amount using force complete replacement');
        return true;
    }

    // If that fails, try the page script approach
    console.log('Aggressive approach failed, trying standard page script approach');
    const pageScriptResult = await setAmountViaPageScript(amount);
    if (pageScriptResult) {
        console.log('Successfully set amount using page script');
        return true;
    }

    // If that fails, try to find the input and inject a more specific script
    const inputResult = await findAmountInputViaPageScript();
    if (inputResult.success && inputResult.details) {
        console.log('Found amount input, details:', inputResult.details);

        // Create a custom script to target this specific input
        let customScript = '';

        if (inputResult.details.id) {
            // If the input has an ID, use that
            customScript = `
                (function() {
                    const input = document.getElementById('${inputResult.details.id}');
                    if (input) {
                        // Try multiple approaches
                        try {
                            // Direct value setting
                            input.value = '${amount}';
                            input.setAttribute('value', '${amount}');

                            // Dispatch events
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));

                            // Try jQuery if available
                            if (typeof jQuery !== 'undefined') {
                                jQuery(input).val('${amount}').trigger('input').trigger('change');
                            }

                            console.log('Set amount to ${amount} on input with ID ${inputResult.details.id}');
                        } catch (e) {
                            console.error('Error setting amount:', e);
                        }
                    }
                })();
            `;
        } else if (inputResult.details.className) {
            // If no ID but has a class, use the class
            customScript = `
                (function() {
                    const inputs = document.getElementsByClassName('${inputResult.details.className}');
                    if (inputs.length > 0) {
                        const input = inputs[0];

                        // Try multiple approaches
                        try {
                            // Direct value setting
                            input.value = '${amount}';
                            input.setAttribute('value', '${amount}');

                            // Dispatch events
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));

                            // Try jQuery if available
                            if (typeof jQuery !== 'undefined') {
                                jQuery(input).val('${amount}').trigger('input').trigger('change');
                            }

                            console.log('Set amount to ${amount} on input with class ${inputResult.details.className}');
                        } catch (e) {
                            console.error('Error setting amount:', e);
                        }
                    }
                })();
            `;
        } else {
            // If no ID or class, try a more generic approach
            customScript = `
                (function() {
                    // Try to find the amount input
                    const amountInputs = document.querySelectorAll('input[type="number"], input[type="text"]:not([type="hidden"]), [contenteditable="true"]');

                    for (const input of amountInputs) {
                        if (input.placeholder?.toLowerCase().includes('amount') ||
                            input.name?.toLowerCase().includes('amount') ||
                            input.className?.toLowerCase().includes('amount') ||
                            input.id?.toLowerCase().includes('amount') ||
                            (input.parentElement && input.parentElement.textContent.toLowerCase().includes('amount'))) {

                            // Try multiple approaches
                            try {
                                if (input.tagName === 'INPUT') {
                                    // Direct value setting for input elements
                                    input.value = '${amount}';
                                    input.setAttribute('value', '${amount}');
                                } else {
                                    // For contentEditable elements
                                    input.textContent = '${amount}';
                                }

                                // Dispatch events
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));

                                // Try jQuery if available
                                if (typeof jQuery !== 'undefined') {
                                    jQuery(input).val('${amount}').trigger('input').trigger('change');
                                }

                                console.log('Set amount to ${amount} on input:', input);
                                break;
                            } catch (e) {
                                console.error('Error setting amount:', e);
                            }
                        }
                    }
                })();
            `;
        }

        // Inject the custom script
        const injectionResult = await injectCustomScript(customScript);
        if (injectionResult) {
            console.log('Successfully injected custom script to set amount');
            return true;
        }
    }

    // If all else fails, try a last-resort approach with a very aggressive script
    const lastResortScript = `
        (function() {
            // First check for the amount field in the modal UI
            const amountField = document.querySelector('.amount-field');
            if (amountField) {
                try {
                    console.log('Found amount field in modal, current value:', amountField.textContent);

                    // Try multiple clearing approaches
                    const clearingApproaches = [
                        // Approach 1: Set to empty or just the currency symbol
                        () => {
                            amountField.textContent = '$';
                            amountField.dispatchEvent(new Event('input', { bubbles: true }));
                            amountField.dispatchEvent(new Event('change', { bubbles: true }));
                        },
                        // Approach 2: Use innerHTML
                        () => {
                            amountField.innerHTML = '$';
                            amountField.dispatchEvent(new Event('input', { bubbles: true }));
                            amountField.dispatchEvent(new Event('change', { bubbles: true }));
                        },
                        // Approach 3: Try to replace the entire content
                        () => {
                            const parent = amountField.parentElement;
                            if (parent) {
                                const originalHTML = parent.innerHTML;
                                const newHTML = originalHTML.replace(/>([^<]*\\d[^<]*)</g, '>$<');
                                parent.innerHTML = newHTML;
                            }
                        },
                        // Approach 4: Try to use DOM manipulation to recreate the element
                        () => {
                            const parent = amountField.parentElement;
                            if (parent) {
                                const newField = document.createElement('div');
                                newField.className = amountField.className;
                                newField.textContent = '$';
                                parent.replaceChild(newField, amountField);
                            }
                        }
                    ];

                    // Try each approach
                    for (let i = 0; i < clearingApproaches.length; i++) {
                        try {
                            clearingApproaches[i]();
                            console.log('Applied clearing approach ' + (i+1));
                        } catch (e) {
                            console.error('Error with clearing approach ' + (i+1) + ':', e);
                        }
                    }

                    // Wait a tiny bit to ensure the clearing takes effect
                    setTimeout(() => {
                        // Find the field again after our clearing attempts
                        const updatedField = document.querySelector('.amount-field');
                        if (updatedField) {
                            // Now set the new value
                            updatedField.textContent = '$${amount}';
                            console.log('Set amount field text to: $${amount}');

                            // Dispatch events
                            updatedField.dispatchEvent(new Event('input', { bubbles: true }));
                            updatedField.dispatchEvent(new Event('change', { bubbles: true }));

                            // Also try innerHTML
                            updatedField.innerHTML = '$${amount}';

                            // Try script injection
                            const script = document.createElement('script');
                            script.textContent = `
                                (function() {
                                    const field = document.querySelector('.amount-field');
                                    if (field) {
                                        field.textContent = '$${amount}';
                                        field.innerHTML = '$${amount}';
                                        field.dispatchEvent(new Event('input', { bubbles: true }));
                                        field.dispatchEvent(new Event('change', { bubbles: true }));
                                    }
                                })();
                            `;
                            document.head.appendChild(script);
                            document.head.removeChild(script);
                        }
                    }, 100);

                    return;
                } catch (e) {
                    console.error('Error setting amount field:', e);
                }
            }

            // Try to find any input that might be the amount input
            const allInputs = document.querySelectorAll('input, [contenteditable="true"]');
            console.log('Searching through ' + allInputs.length + ' potential inputs');

            // Look for inputs near "Amount" text first
            const amountLabels = document.querySelectorAll('div, span, label');
            for (const label of amountLabels) {
                if (label.textContent.toLowerCase().includes('amount')) {
                    // Look for inputs that are siblings or children of this element
                    const parent = label.parentElement;
                    const inputs = parent.querySelectorAll('input');
                    if (inputs.length > 0) {
                        try {
                            // First clear the input
                            inputs[0].value = '';
                            inputs[0].dispatchEvent(new Event('input', { bubbles: true }));

                            // Wait a tiny bit
                            setTimeout(() => {
                                // Set the new value
                                inputs[0].value = '${amount}';
                                inputs[0].dispatchEvent(new Event('input', { bubbles: true }));
                                inputs[0].dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('Set amount on input near "Amount" text');
                            }, 50);

                            return;
                        } catch (e) {
                            console.error('Error setting amount:', e);
                        }
                    }
                }
            }

            // Try all inputs as a last resort
            for (const input of allInputs) {
                try {
                    if (input.tagName === 'INPUT') {
                        const originalValue = input.value;

                        // First clear the input
                        input.value = '';
                        input.dispatchEvent(new Event('input', { bubbles: true }));

                        // Then set the new value
                        input.value = '${amount}';
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));

                        console.log('Set amount on input, original value: ' + originalValue);
                    } else if (input.contentEditable === 'true') {
                        const originalValue = input.textContent;

                        // First clear the content
                        input.textContent = '';
                        input.dispatchEvent(new Event('input', { bubbles: true }));

                        // Then set the new content
                        input.textContent = '${amount}';
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));

                        console.log('Set amount on contentEditable element, original value: ' + originalValue);
                    }
                } catch (e) {
                    // Ignore errors and continue trying other inputs
                }
            }

            // Try one more approach - look for the block--bet-amount element
            const betAmountBlock = document.querySelector('.block--bet-amount');
            if (betAmountBlock) {
                console.log('Found bet amount block, trying to interact with it');

                // Click it to potentially open the modal
                betAmountBlock.click();

                // Try to find and click any child elements
                const childElements = betAmountBlock.querySelectorAll('*');
                for (const child of childElements) {
                    try {
                        child.click();
                    } catch (e) {
                        // Ignore errors
                    }
                }
            }
        })();
    `;

    const lastResortResult = await injectCustomScript(lastResortScript);
    console.log('Last resort script injection result:', lastResortResult);

    // We've tried everything, so return the result of the last attempt
    return lastResortResult;
}

// Execute a trade via the page script with safety checks
function executeTradeViaPageScript(direction, amount) {
    return new Promise((resolve) => {
        try {
            console.log('Executing trade via page script with safety checks:', direction, amount);

            // First, try to force a complete amount replacement to ensure the correct amount
            forceCompleteAmountReplacement(amount).then(() => {
                console.log('Forced complete amount replacement before trade execution');

                // Create a message handler to listen for the response
                const messageHandler = function(event) {
                    // Only accept messages from the same frame
                    if (event.source !== window) return;

                    const message = event.data;

                    // Check if this is a trade execution response from our page script
                    if (message && message.from === 'pocketOptionBot' && message.action === 'tradeExecuted') {
                        console.log('Trade execution result from page script:', message);

                        // Remove the event listener
                        window.removeEventListener('message', messageHandler);

                        if (message.success) {
                            // Check if the amount was verified
                            if (message.verifiedAmount) {
                                const verifiedAmount = parseFloat(message.verifiedAmount);
                                const expectedAmount = parseFloat(amount);

                                if (Math.abs(verifiedAmount - expectedAmount) < 0.001) {
                                    console.log('Trade executed with verified correct amount:', verifiedAmount);
                                    showNotification(`Trade executed with verified amount: $${verifiedAmount}`, 'success');
                                    resolve(true);
                                } else {
                                    console.error('CRITICAL ERROR: Trade executed with WRONG amount!',
                                                'Expected:', expectedAmount, 'Actual:', verifiedAmount);
                                    showNotification(`WARNING: Trade executed with WRONG amount: $${verifiedAmount} instead of $${expectedAmount}`, 'error');

                                    // Still resolve as true since the trade was executed, but with a warning
                                    resolve(true);
                                }
                            } else {
                                console.log('Trade executed but amount verification not available');
                                resolve(message.success);
                            }
                        } else {
                            console.error('Trade execution failed:', message.error);
                            resolve(false);
                        }
                    }
                };

                // Add the message handler
                window.addEventListener('message', messageHandler);

                // Send a message to the page script to execute the trade
                window.postMessage({
                    action: 'executeTrade',
                    direction: direction,
                    amount: amount
                }, '*');

                // Set a timeout to resolve the promise if no response is received
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    console.log('No response from page script, assuming trade failed');
                    resolve(false);
                }, 5000);
            });
        } catch (e) {
            console.error('Error executing trade via page script:', e);
            resolve(false);
        }
    });
}

// Debug function to help troubleshoot issues
function debugPocketOption() {
    console.log('Debugging Pocket Option integration');

    // Check if we're on Pocket Option
    if (!window.location.href.includes('pocketoption.com')) {
        console.error('Not on Pocket Option website');
        return;
    }

    // Check DOM structure
    console.log('Document body:', document.body);
    console.log('Document head:', document.head);

    // Check for iframes
    const iframes = document.querySelectorAll('iframe');
    console.log('Iframes on page:', iframes.length);
    iframes.forEach((iframe, index) => {
        console.log(`Iframe ${index}:`, iframe);
    });

    // Check for our floating window
    const floatingWindowElement = document.getElementById('botFloatingWindow');
    console.log('Floating window element:', floatingWindowElement);

    // Check z-index of elements
    const allElements = document.querySelectorAll('*');
    const highZIndexElements = Array.from(allElements).filter(el => {
        const style = window.getComputedStyle(el);
        const zIndex = parseInt(style.zIndex);
        return !isNaN(zIndex) && zIndex > 1000;
    });

    console.log('Elements with high z-index:', highZIndexElements);

    // Force create the floating interface
    console.log('Debug function is forcing creation of floating interface');
    createFloatingInterface();

    // Show a notification to confirm the debug function ran
    showNotification('Debug function executed - check console for details', 'info');
}

// Load settings from storage
function loadSettings() {
    chrome.storage.local.get('settings', function(data) {
        if (data.settings) {
            settings = data.settings;

            // Ensure the martingale option exists
            if (settings.quantumSettings && settings.quantumSettings.useMartingale === undefined) {
                settings.quantumSettings.useMartingale = true; // Enable martingale by default
            }

            // Send settings to floating interface if it exists
            if (floatingFrame) {
                floatingFrame.contentWindow.postMessage({
                    action: 'loadSettings',
                    settings: settings
                }, '*');
            }

            // Save the updated settings
            saveSettings(settings);
        } else {
            // Create default settings if none exist
            settings = {
                mode: 'quantum', // Default mode
                quantumSettings: {
                    riskPercentage: 10,
                    profitTarget: 10,
                    timePeriod: 'S5',
                    useMartingale: true // Enable martingale by default
                },
                randomSettings: {
                    minAmount: 1,
                    maxAmount: 10,
                    minExpiry: 30,
                    maxExpiry: 180
                },
                masanielloSettings: {
                    baseAmount: 1,
                    step: 1,
                    maxStep: 5
                }
            };

            // Save the default settings
            saveSettings(settings);

            // Send settings to floating interface if it exists
            if (floatingFrame) {
                floatingFrame.contentWindow.postMessage({
                    action: 'loadSettings',
                    settings: settings
                }, '*');
            }
        }
    });
}

// Save settings to storage
function saveSettings(newSettings) {
    settings = {...settings, ...newSettings};
    chrome.storage.local.set({settings});
}

// Listen for messages from the popup
chrome.runtime.onMessage.addListener(function(message, _sender, sendResponse) {
    console.log('Received message from popup:', message);

    if (message.action === 'connect') {
        // Connect to Pocket Option
        if (!isConnected) {
            const success = initializeBot();

            // If connection was successful, the floating interface will be created automatically
            // by the initializeBot function

            sendResponse({
                success: success,
                balance: currentBalance
            });
        } else {
            // Already connected, just create the floating interface
            createFloatingInterface();

            sendResponse({
                success: true,
                balance: currentBalance
            });
        }

        return true; // Keep the message channel open for the async response
    }
    else if (message.action === 'executeTrade') {
        // Execute a trade
        const direction = message.direction;
        const amount = message.amount;
        const expiry = message.expiry || 60; // Default to 60 seconds if not specified

        console.log('Executing trade from popup:', direction, amount, expiry);

        executeTrade(direction, amount, expiry)
            .then(success => {
                sendResponse({ success });
            })
            .catch(error => {
                console.error('Error executing trade:', error);
                sendResponse({ success: false, error: error.message });
            });

        return true; // Keep the message channel open for the async response
    }
    else if (message.action === 'openFloatingInterface') {
        console.log('Received openFloatingInterface message');

        // Create the floating interface
        createFloatingInterface();

        // Load settings
        loadSettings();

        sendResponse({ success: true });
    }
    else if (message.action === 'closeFloatingInterface') {
        removeFloatingInterface();
        sendResponse({ success: true });
    }
    else if (message.action === 'debugPocketOption') {
        debugPocketOption();
        sendResponse({ success: true });
    }

    return true;
});

// We'll use the existing debounce mechanism in executeTrade function

// Listen for messages from the floating interface
window.addEventListener('message', function(event) {
    // Log all messages for debugging
    console.log('Message received in content.js:', event.data, 'Source:', event.source);

    // Only accept messages from our floating interface
    if (floatingFrame && event.source === floatingFrame.contentWindow) {
        const message = event.data;
        console.log('Received message from floating interface:', message);

        if (message.action === 'executeTrade') {
            // Execute a trade
            const direction = message.direction;
            const amount = message.amount;
            const expiry = message.expiry || 60; // Default to 60 seconds if not specified

            // We'll rely on the debounce mechanism in executeTrade function

            console.log('Executing trade from floating interface:', direction, amount, expiry);

            // Create a visual indicator that we're executing a trade
            const tradeIndicator = document.createElement('div');
            tradeIndicator.style.position = 'fixed';
            tradeIndicator.style.top = '50%';
            tradeIndicator.style.left = '50%';
            tradeIndicator.style.transform = 'translate(-50%, -50%)';
            tradeIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            tradeIndicator.style.color = 'white';
            tradeIndicator.style.padding = '20px';
            tradeIndicator.style.borderRadius = '10px';
            tradeIndicator.style.zIndex = '10000000';
            tradeIndicator.style.fontFamily = 'Arial, sans-serif';
            tradeIndicator.style.fontSize = '18px';
            tradeIndicator.style.textAlign = 'center';
            tradeIndicator.innerHTML = `<div>Executing ${direction} trade</div><div style="margin-top:10px;font-size:14px;">Please wait...</div>`;
            document.body.appendChild(tradeIndicator);

            // Show notification to confirm trade execution attempt
            showNotification(`Attempting to execute ${direction} trade`, 'info');

            // Scan for trading elements before executing the trade
            scanForTradingElements();

            // Add a small delay before executing the trade to ensure UI updates and elements are found
            setTimeout(() => {
                // Generate a unique ID for this trade to track it
                const tradeId = `trade_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                console.log(`Starting trade execution with ID: ${tradeId}`);

                // Track if we've already sent a result for this trade to prevent duplicates
                let resultSent = false;

                executeTrade(direction, amount, expiry)
                    .then(success => {
                        console.log(`Trade execution result for ID ${tradeId}:`, success);

                        // Remove the trade indicator
                        if (document.body.contains(tradeIndicator)) {
                            document.body.removeChild(tradeIndicator);
                        }

                        if (resultSent) {
                            console.log(`Result already sent for trade ID ${tradeId}, skipping`);
                            return;
                        }

                        resultSent = true;

                        if (success) {
                            showNotification(`Successfully executed ${direction} trade`, 'success');

                            // Send a message back to the floating interface to update the trade status
                            floatingFrame.contentWindow.postMessage({
                                action: 'tradeExecuted',
                                success: true,
                                direction: direction,
                                amount: amount,
                                tradeId: tradeId
                            }, '*');

                            // After a short delay, also send a simulated trade result
                            // This ensures the bot continues trading even if the real result isn't detected
                            setTimeout(() => {
                                console.log(`Sending simulated trade result for ID ${tradeId}`);
                                floatingFrame.contentWindow.postMessage({
                                    action: 'tradeResult',
                                    result: Math.random() < 0.5 ? 'WIN' : 'LOSS',
                                    tradeId: tradeId
                                }, '*');
                            }, 5000);
                        } else {
                            showNotification(`Failed to execute ${direction} trade`, 'error');

                            // Send a message back to the floating interface to update the trade status
                            floatingFrame.contentWindow.postMessage({
                                action: 'tradeExecuted',
                                success: false,
                                direction: direction,
                                amount: amount,
                                error: 'Failed to execute trade',
                                tradeId: tradeId
                            }, '*');

                            // Also send a reset message to ensure the bot can continue trading
                            setTimeout(() => {
                                console.log(`Sending trade reset for failed trade ID ${tradeId}`);
                                floatingFrame.contentWindow.postMessage({
                                    action: 'resetTrade',
                                    tradeId: tradeId
                                }, '*');
                            }, 2000);
                        }
                    })
                    .catch(error => {
                        console.error(`Error executing trade ID ${tradeId}:`, error);
                        showNotification('Error executing trade: ' + error.message, 'error');

                        // Remove the trade indicator
                        if (document.body.contains(tradeIndicator)) {
                            document.body.removeChild(tradeIndicator);
                        }

                        if (resultSent) {
                            console.log(`Result already sent for trade ID ${tradeId}, skipping error handling`);
                            return;
                        }

                        resultSent = true;

                        // Send a message back to the floating interface to update the trade status
                        floatingFrame.contentWindow.postMessage({
                            action: 'tradeExecuted',
                            success: false,
                            direction: direction,
                            amount: amount,
                            error: error.message,
                            tradeId: tradeId
                        }, '*');

                        // Also send a reset message to ensure the bot can continue trading
                        setTimeout(() => {
                            console.log(`Sending trade reset for error trade ID ${tradeId}`);
                            floatingFrame.contentWindow.postMessage({
                                action: 'resetTrade',
                                tradeId: tradeId
                            }, '*');
                        }, 2000);
                    });
            }, 1000);
        }
        else if (message.action === 'closeFloatingWindow') {
            removeFloatingInterface();
        }
        else if (message.action === 'showNotification') {
            showNotification(message.message, message.type);
        }
        else if (message.action === 'saveSettings') {
            saveSettings(message.settings);
        }
        else if (message.action === 'setMode') {
            console.log('Setting mode to:', message.mode);
            // Any additional mode-specific actions can be added here
        }
        else if (message.action === 'updatePosition') {
            // We don't need to do anything here as the dragging is handled directly on the container
            console.log('Received position update from iframe');
        }
        else if (message.action === 'toggleLock') {
            // Handle locking/unlocking the container
            if (floatingWindow) {
                const header = floatingWindow.querySelector('div:first-child');
                if (header) {
                    header.style.pointerEvents = message.isLocked ? 'none' : 'auto';
                    console.log('Container ' + (message.isLocked ? 'locked' : 'unlocked'));
                }
            }
        }
        else if (message.action === 'showCongratulationsPopup') {
            // Show a congratulations popup
            console.log('Showing congratulations popup for profit:', message.profit);
            showCongratulationsPopup(message.profit);
        }
        else if (message.action === 'showEncouragementPopup') {
            // Show an encouragement popup
            console.log('Showing encouragement popup for loss:', message.loss);
            showEncouragementPopup(message.loss);
        }
    }
});

/**
 * Show a congratulations popup when profit target is reached
 */
function showCongratulationsPopup(profit) {
    console.log('Profit target reached! Total profit: $' + profit.toFixed(2));

    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '99999999'; // Extremely high z-index to ensure it's above everything
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';

    // Create popup
    const popup = document.createElement('div');
    popup.style.backgroundColor = '#1e293b';
    popup.style.borderRadius = '10px';
    popup.style.padding = '30px';
    popup.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.5)';
    popup.style.textAlign = 'center';
    popup.style.maxWidth = '400px';
    popup.style.width = '80%';
    popup.style.border = '2px solid #10b981';
    popup.style.color = 'white';
    popup.style.fontFamily = 'Arial, sans-serif';
    popup.style.position = 'relative';

    // Add confetti animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes confetti-fall {
            0% { transform: translateY(-100px) rotate(0deg); opacity: 1; }
            100% { transform: translateY(1000px) rotate(720deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Create confetti elements
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.position = 'absolute';
        confetti.style.width = Math.random() * 10 + 5 + 'px';
        confetti.style.height = Math.random() * 10 + 5 + 'px';
        confetti.style.backgroundColor = getRandomColor();
        confetti.style.top = '-20px';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.animation = `confetti-fall ${1 + Math.random() * 3}s ease-in-out ${Math.random() * 2}s infinite`;
        confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
        confetti.style.zIndex = '100000000'; // Even higher z-index for confetti
        overlay.appendChild(confetti);
    }

    // Add content
    const title = document.createElement('h2');
    title.textContent = '🎉 Congratulations! 🎉';
    title.style.color = '#10b981';
    title.style.fontSize = '24px';
    title.style.marginBottom = '15px';

    const message = document.createElement('p');
    message.innerHTML = `You've reached your profit target!<br>Total profit: <strong>$${profit.toFixed(2)}</strong>`;
    message.style.fontSize = '16px';
    message.style.lineHeight = '1.5';
    message.style.marginBottom = '20px';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.padding = '10px 20px';
    closeButton.style.backgroundColor = '#10b981';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '14px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.transition = 'background-color 0.2s';

    closeButton.addEventListener('mouseover', () => {
        closeButton.style.backgroundColor = '#0d9668';
    });

    closeButton.addEventListener('mouseout', () => {
        closeButton.style.backgroundColor = '#10b981';
    });

    closeButton.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    // Assemble popup
    popup.appendChild(title);
    popup.appendChild(message);
    popup.appendChild(closeButton);
    overlay.appendChild(popup);

    // Add to document - ensure we're adding to the top-level document
    const targetDocument = window.top ? window.top.document : document;
    targetDocument.body.appendChild(overlay);

    console.log('Congratulations popup added to document body with high z-index');

    // Auto close after 10 seconds
    setTimeout(() => {
        if (targetDocument.body.contains(overlay)) {
            targetDocument.body.removeChild(overlay);
            console.log('Congratulations popup automatically removed after timeout');
        }
    }, 10000);
}

/**
 * Show an encouragement popup when win target is reached but with a loss
 */
function showEncouragementPopup(loss) {
    console.log('Win target reached but with loss. Total loss: $' + loss.toFixed(2));

    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '99999999'; // Extremely high z-index to ensure it's above everything
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';

    // Create popup
    const popup = document.createElement('div');
    popup.style.backgroundColor = '#1e293b';
    popup.style.borderRadius = '10px';
    popup.style.padding = '30px';
    popup.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.5)';
    popup.style.textAlign = 'center';
    popup.style.maxWidth = '400px';
    popup.style.width = '80%';
    popup.style.border = '2px solid #f59e0b'; // Orange border for encouragement
    popup.style.color = 'white';
    popup.style.fontFamily = 'Arial, sans-serif';
    popup.style.position = 'relative';

    // Add content
    const title = document.createElement('h2');
    title.textContent = '⚡ Keep Going! ⚡';
    title.style.color = '#f59e0b'; // Orange color for encouragement
    title.style.fontSize = '24px';
    title.style.marginBottom = '15px';

    const message = document.createElement('p');
    message.innerHTML = `You've reached your win target, but with a loss of <strong>$${loss.toFixed(2)}</strong>.<br><br>
                        Trading has its ups and downs. Consider adjusting your strategy and try again!<br><br>
                        <strong>Tips:</strong><br>
                        • Check your payout percentage<br>
                        • Adjust your risk settings<br>
                        • Try a different time period`;
    message.style.fontSize = '16px';
    message.style.lineHeight = '1.5';
    message.style.marginBottom = '20px';
    message.style.textAlign = 'left';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Try Again';
    closeButton.style.padding = '10px 20px';
    closeButton.style.backgroundColor = '#f59e0b';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '14px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.transition = 'background-color 0.2s';

    closeButton.addEventListener('mouseover', () => {
        closeButton.style.backgroundColor = '#d97706';
    });

    closeButton.addEventListener('mouseout', () => {
        closeButton.style.backgroundColor = '#f59e0b';
    });

    closeButton.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    // Assemble popup
    popup.appendChild(title);
    popup.appendChild(message);
    popup.appendChild(closeButton);
    overlay.appendChild(popup);

    // Add to document - ensure we're adding to the top-level document
    const targetDocument = window.top ? window.top.document : document;
    targetDocument.body.appendChild(overlay);

    console.log('Encouragement popup added to document body with high z-index');

    // Auto close after 15 seconds
    setTimeout(() => {
        if (targetDocument.body.contains(overlay)) {
            targetDocument.body.removeChild(overlay);
            console.log('Encouragement popup automatically removed after timeout');
        }
    }, 15000);
}

// Helper function to generate random colors for confetti
function getRandomColor() {
    const colors = ['#4CAF50', '#FFC107', '#2196F3', '#E91E63', '#9C27B0'];
    return colors[Math.floor(Math.random() * colors.length)];
}